-- ========================================
-- NTA 3.0 规则管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 规则管理模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- 注意：检测规则配置和威胁检测算法配置已在02-th_analysis.sql中定义
-- ========================================

-- ========================================
-- 过滤规则管理表
-- ========================================

-- 过滤规则表
DROP TABLE IF EXISTS filter_rule CASCADE;

CREATE TABLE filter_rule (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    filter_json JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash VARCHAR(255) NOT NULL,
    criteria filter_criteria_enum NOT NULL,
    active BOOLEAN NOT NULL DEFAULT true
);

COMMENT ON TABLE filter_rule IS '过滤规则表';
COMMENT ON COLUMN filter_rule.task_id IS '任务ID';
COMMENT ON COLUMN filter_rule.ip IS 'IP地址';
COMMENT ON COLUMN filter_rule.filter_json IS '过滤规则JSON字符串';
COMMENT ON COLUMN filter_rule.created_at IS '创建时间';
COMMENT ON COLUMN filter_rule.updated_at IS '更新时间';
COMMENT ON COLUMN filter_rule.hash IS '规则hash值，用于校验重复';
COMMENT ON COLUMN filter_rule.criteria IS '过滤条件，使用filter_criteria_enum枚举';
COMMENT ON COLUMN filter_rule.active IS '是否激活 true:正常 false:删除';

-- 过滤模式表
DROP TABLE IF EXISTS filter_mode CASCADE;

CREATE TABLE filter_mode (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    drop_mode BOOLEAN NOT NULL
);

COMMENT ON TABLE filter_mode IS '过滤模式表';
COMMENT ON COLUMN filter_mode.task_id IS '任务ID';
COMMENT ON COLUMN filter_mode.drop_mode IS '是否丢弃模式 false表示保留，true表示丢弃';

-- ========================================
-- 模型管理表（规则管理范畴）
-- ========================================

-- 模型信息表
DROP TABLE IF EXISTS tb_model_info CASCADE;

CREATE TABLE tb_model_info (
    model_id SERIAL PRIMARY KEY,
    model_name VARCHAR(255) NOT NULL,
    model_algorithm VARCHAR(100) NOT NULL,
    model_type VARCHAR(100),
    model_version VARCHAR(50),
    remark TEXT,
    state INTEGER NOT NULL DEFAULT 1,
    model_config TEXT,
    model_path VARCHAR(500),
    model_hash VARCHAR(128),
    priority INTEGER DEFAULT 100,
    tags VARCHAR(500),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    is_deleted INTEGER DEFAULT 0
);

COMMENT ON TABLE tb_model_info IS '模型信息表';
COMMENT ON COLUMN tb_model_info.model_id IS '模型ID';
COMMENT ON COLUMN tb_model_info.model_name IS '模型名称';
COMMENT ON COLUMN tb_model_info.model_algorithm IS '模型算法类型';
COMMENT ON COLUMN tb_model_info.model_type IS '模型类型';
COMMENT ON COLUMN tb_model_info.model_version IS '模型版本';
COMMENT ON COLUMN tb_model_info.remark IS '模型描述';
COMMENT ON COLUMN tb_model_info.state IS '模型状态：0-失效，1-生效';
COMMENT ON COLUMN tb_model_info.model_config IS '模型配置参数（JSON格式）';
COMMENT ON COLUMN tb_model_info.model_path IS '模型文件路径';
COMMENT ON COLUMN tb_model_info.model_hash IS '模型文件哈希值';
COMMENT ON COLUMN tb_model_info.priority IS '模型优先级';
COMMENT ON COLUMN tb_model_info.tags IS '模型标签（多个标签用逗号分隔）';
COMMENT ON COLUMN tb_model_info.created_time IS '创建时间';
COMMENT ON COLUMN tb_model_info.updated_time IS '更新时间';
COMMENT ON COLUMN tb_model_info.created_by IS '创建者';
COMMENT ON COLUMN tb_model_info.updated_by IS '更新者';
COMMENT ON COLUMN tb_model_info.is_deleted IS '是否删除：0-未删除，1-已删除';



-- ========================================
-- 规则服务相关表
-- ========================================

-- 规则定义表
DROP TABLE IF EXISTS rule_definitions CASCADE;

CREATE TABLE rule_definitions (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_content TEXT NOT NULL,
    rule_format VARCHAR(20) NOT NULL DEFAULT 'JSON',
    priority INTEGER DEFAULT 0,
    enabled BOOLEAN NOT NULL DEFAULT true,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

COMMENT ON TABLE rule_definitions IS '规则定义表';
COMMENT ON COLUMN rule_definitions.rule_format IS '规则格式: JSON, YAML, DROOLS';
COMMENT ON COLUMN rule_definitions.priority IS '优先级，数值越大优先级越高';

-- 规则执行记录表
DROP TABLE IF EXISTS rule_executions CASCADE;

CREATE TABLE rule_executions (
    id BIGSERIAL PRIMARY KEY,
    rule_id INTEGER REFERENCES rule_definitions (id),
    input_data JSONB,
    output_data JSONB,
    execution_time_ms INTEGER,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE rule_executions IS '规则执行记录表';
COMMENT ON COLUMN rule_executions.rule_id IS '关联的规则ID';
COMMENT ON COLUMN rule_executions.input_data IS '输入数据（JSON格式）';
COMMENT ON COLUMN rule_executions.output_data IS '输出数据（JSON格式）';
COMMENT ON COLUMN rule_executions.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN rule_executions.status IS '执行状态: SUCCESS, FAILED, TIMEOUT';
COMMENT ON COLUMN rule_executions.error_message IS '错误信息';
COMMENT ON COLUMN rule_executions.executed_at IS '执行时间';

-- 特征规则表（用于告警关联查询）
DROP TABLE IF EXISTS feature_rule CASCADE;

CREATE TABLE feature_rule (
    id BIGINT PRIMARY KEY,
    rule_name VARCHAR(200),
    rule_type VARCHAR(50),
    rule_content TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE feature_rule IS '特征规则表';
COMMENT ON COLUMN feature_rule.id IS '规则ID';
COMMENT ON COLUMN feature_rule.rule_name IS '规则名称';
COMMENT ON COLUMN feature_rule.rule_type IS '规则类型';
COMMENT ON COLUMN feature_rule.rule_content IS '规则内容';
COMMENT ON COLUMN feature_rule.enabled IS '是否启用';

-- ========================================
-- 流量白名单管理表
-- ========================================

-- 流量白名单表
DROP TABLE IF EXISTS traffic_whitelist CASCADE;

CREATE TABLE traffic_whitelist (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(50) NOT NULL DEFAULT '',
    port INTEGER NOT NULL DEFAULT -1,
    app_id INTEGER NOT NULL DEFAULT -1,
    rule_id INTEGER NOT NULL DEFAULT -1,
    rule_level INTEGER NOT NULL DEFAULT -1,
    rule_name TEXT NOT NULL DEFAULT '',
    rule_desc TEXT NOT NULL DEFAULT '',
    rule_state VARCHAR(255) NOT NULL DEFAULT '生效',
    rule_size BIGINT NOT NULL DEFAULT 0,
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_size_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    capture_mode INTEGER NOT NULL DEFAULT 0,
    rule_json JSON NOT NULL DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rule_hash VARCHAR(255) NOT NULL DEFAULT '',
    cyber_kill_chain cyber_kill_chain_enum NOT NULL DEFAULT 'OTHER',
    task_id INTEGER NOT NULL DEFAULT 0
);

COMMENT ON TABLE traffic_whitelist IS '流量白名单表';
COMMENT ON COLUMN traffic_whitelist.server_ip IS '服务器IP';
COMMENT ON COLUMN traffic_whitelist.port IS '端口';
COMMENT ON COLUMN traffic_whitelist.app_id IS '应用id';
COMMENT ON COLUMN traffic_whitelist.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';

-- 流量白名单日志表
DROP TABLE IF EXISTS traffic_whitelist_log CASCADE;

CREATE TABLE traffic_whitelist_log (
    id SERIAL PRIMARY KEY,
    last_filter VARCHAR(50) NOT NULL,
    last_total VARCHAR(50) NOT NULL,
    white_list_id INTEGER NOT NULL
);

COMMENT ON TABLE traffic_whitelist_log IS '流量白名单日志表';
COMMENT ON COLUMN traffic_whitelist_log.last_filter IS '上次过滤日志数量';
COMMENT ON COLUMN traffic_whitelist_log.last_total IS '上次全部日志数量';
COMMENT ON COLUMN traffic_whitelist_log.white_list_id IS '外键 white_list 的主键';

-- 流量白名单状态表
DROP TABLE IF EXISTS traffic_whitelist_state CASCADE;

CREATE TABLE traffic_whitelist_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    UNIQUE (task_id)
);

COMMENT ON TABLE traffic_whitelist_state IS '流量白名单状态表';
COMMENT ON COLUMN traffic_whitelist_state.task_id IS '任务ID';
COMMENT ON COLUMN traffic_whitelist_state.state IS '白名单状态：0-停用，1-启用';

-- ========================================
-- 创建索引
-- ========================================

-- 过滤规则表索引
CREATE INDEX idx_filter_rule_task_id ON filter_rule (task_id);
CREATE INDEX idx_filter_rule_ip ON filter_rule (ip);
CREATE INDEX idx_filter_rule_criteria ON filter_rule (criteria);
CREATE INDEX idx_filter_rule_active ON filter_rule (active);
CREATE INDEX idx_filter_rule_hash ON filter_rule (hash);

-- 过滤模式表索引
CREATE INDEX idx_filter_mode_task_id ON filter_mode (task_id);

-- 模型信息表索引
CREATE INDEX idx_tb_model_info_model_name ON tb_model_info (model_name);
CREATE INDEX idx_tb_model_info_model_algorithm ON tb_model_info (model_algorithm);
CREATE INDEX idx_tb_model_info_model_type ON tb_model_info (model_type);
CREATE INDEX idx_tb_model_info_state ON tb_model_info (state);
CREATE INDEX idx_tb_model_info_priority ON tb_model_info (priority);
CREATE INDEX idx_tb_model_info_created_time ON tb_model_info (created_time);
CREATE INDEX idx_tb_model_info_updated_time ON tb_model_info (updated_time);
CREATE INDEX idx_tb_model_info_is_deleted ON tb_model_info (is_deleted);

-- 创建唯一索引（模型名称在未删除状态下唯一）
CREATE UNIQUE INDEX uk_model_name_not_deleted ON tb_model_info(model_name) WHERE is_deleted = 0;

-- 规则定义表索引
CREATE INDEX idx_rule_definitions_rule_type ON rule_definitions (rule_type);
CREATE INDEX idx_rule_definitions_enabled ON rule_definitions (enabled);
CREATE INDEX idx_rule_definitions_priority ON rule_definitions (priority);
CREATE INDEX idx_rule_definitions_created_by ON rule_definitions (created_by);

-- 规则执行记录表索引
CREATE INDEX idx_rule_executions_rule_id ON rule_executions (rule_id);
CREATE INDEX idx_rule_executions_status ON rule_executions (status);
CREATE INDEX idx_rule_executions_executed_at ON rule_executions (executed_at);

-- 特征规则表索引
CREATE INDEX idx_feature_rule_rule_type ON feature_rule (rule_type);
CREATE INDEX idx_feature_rule_enabled ON feature_rule (enabled);
CREATE INDEX idx_feature_rule_rule_name ON feature_rule (rule_name);

-- 流量白名单表索引
CREATE INDEX idx_traffic_whitelist_server_ip ON traffic_whitelist (server_ip);
CREATE INDEX idx_traffic_whitelist_task_id ON traffic_whitelist (task_id);
CREATE INDEX idx_traffic_whitelist_rule_state ON traffic_whitelist (rule_state);
CREATE INDEX idx_traffic_whitelist_cyber_kill_chain ON traffic_whitelist (cyber_kill_chain);
CREATE INDEX idx_traffic_whitelist_created_at ON traffic_whitelist (created_at);

-- 流量白名单日志表索引
CREATE INDEX idx_traffic_whitelist_log_white_list_id ON traffic_whitelist_log (white_list_id);

-- 流量白名单状态表索引
CREATE INDEX idx_traffic_whitelist_state_task_id ON traffic_whitelist_state (task_id);
CREATE INDEX idx_traffic_whitelist_state_state ON traffic_whitelist_state (state);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为规则管理表创建更新时间触发器
CREATE TRIGGER update_filter_rule_updated_at 
    BEFORE UPDATE ON filter_rule 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建模型表更新时间触发器函数
CREATE OR REPLACE FUNCTION update_model_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tb_model_info_updated_time 
    BEFORE UPDATE ON tb_model_info 
    FOR EACH ROW EXECUTE FUNCTION update_model_updated_time_column();

CREATE TRIGGER update_rule_definitions_updated_at
    BEFORE UPDATE ON rule_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_feature_rule_updated_at
    BEFORE UPDATE ON feature_rule
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为流量白名单表创建更新时间触发器
CREATE TRIGGER update_traffic_whitelist_updated_at
    BEFORE UPDATE ON traffic_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入模型信息初始数据
INSERT INTO tb_model_info (
    model_id, model_name, model_algorithm, model_type, model_version, remark, state, 
    priority, tags, created_by, updated_by
) VALUES 
-- 内网检测模型
(99001, '智能内网网段检测', '协议识别', '内网检测', '1.0.0', 
 '基于网络流量中的IP地址与MAC地址的对应关系构建IP、MAC的关联图谱，实现相关内网网段的检测', 
 1, 100, '内网,网段,检测', 'system', 'system'),

-- 证书检测模型
(99002, '检测异常证书', '特征识别', '证书检测', '1.0.0', 
 '基于网络流量中出现的海量证书，提取证书中的基础及扩展字段，基于专家知识对其中的异常字段进行挖掘，检测出异常证书', 
 1, 100, '证书,异常,检测', 'system', 'system'),

-- APT检测模型
(99003, '检测APT29组织发起的攻击', '行为识别', 'APT检测', '1.0.0', 
 '基于对已有APT29组织的攻击行为的分析，提取网络节点中相关的IP，指纹，证书等信息构建知识图谱，发掘流量中潜在的APT29组织的攻击', 
 1, 100, 'APT,APT29,攻击检测', 'system', 'system'),

(99004, '检测APT28组织发起的攻击', '行为识别', 'APT检测', '1.0.0', 
 '基于对已有APT28组织的攻击行为的分析，提取网络节点中相关的IP，指纹，证书等信息构建知识图谱，发掘流量中潜在的APT28组织的攻击', 
 1, 100, 'APT,APT28,攻击检测', 'system', 'system'),

-- 挖矿检测模型
(99005, '检测挖矿行为', '特征识别', '挖矿检测', '1.0.0', 
 '基于挖矿软件的网络通信特征，检测网络中的挖矿行为', 
 1, 100, '挖矿,检测', 'system', 'system'),

-- RAT检测模型
(99006, '检测远程访问木马', '行为识别', 'RAT检测', '1.0.0', 
 '基于远程访问木马的通信模式和行为特征，检测网络中的RAT活动', 
 1, 100, 'RAT,木马,检测', 'system', 'system'),

-- Tor检测模型
(99007, '检测Tor匿名网络', '特征识别', 'Tor检测', '1.0.0', 
 '基于Tor网络的通信特征，检测网络中的Tor流量', 
 1, 100, 'Tor,匿名,检测', 'system', 'system'),

-- 指纹检测模型
(99008, 'SSL指纹检测', '特征识别', '指纹检测', '1.0.0', 
 '基于SSL/TLS握手过程中的指纹特征，检测异常的SSL通信', 
 1, 100, 'SSL,指纹,检测', 'system', 'system'),

-- DNS检测模型
(99009, 'DNS隧道检测', '行为识别', 'DNS检测', '1.0.0', 
 '基于DNS查询的异常模式，检测DNS隧道通信', 
 1, 100, 'DNS,隧道,检测', 'system', 'system'),

-- 暴力破解检测模型
(99010, 'Web登录暴力破解检测', '行为识别', '暴力破解检测', '1.0.0', 
 '基于Web登录请求的频率和模式，检测暴力破解攻击', 
 1, 100, 'Web,暴力破解,检测', 'system', 'system');

-- 重置序列
SELECT setval('tb_model_info_model_id_seq', (SELECT MAX(model_id) FROM tb_model_info));

-- ========================================
-- 创建视图
-- ========================================

-- 活跃模型视图
CREATE OR REPLACE VIEW v_active_models AS
SELECT 
    model_id,
    model_name,
    model_algorithm,
    model_type,
    model_version,
    remark,
    priority,
    tags,
    created_time,
    updated_time,
    created_by
FROM tb_model_info 
WHERE state = 1 AND is_deleted = 0
ORDER BY priority DESC, created_time DESC;

COMMENT ON VIEW v_active_models IS '活跃模型视图，显示所有生效且未删除的模型';

-- 规则统计视图
CREATE OR REPLACE VIEW v_rule_statistics AS
SELECT 
    rule_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN enabled = true THEN 1 END) as enabled_count,
    COUNT(CASE WHEN enabled = false THEN 1 END) as disabled_count,
    AVG(priority) as avg_priority
FROM rule_definitions
GROUP BY rule_type;

COMMENT ON VIEW v_rule_statistics IS '规则统计视图，按类型统计规则数量和状态';
