-- ========================================
-- Task Service Database Schema
-- 任务服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE task_service;
-- \c task_service;

-- ========================================
-- 核心任务管理表
-- ========================================

-- 任务表 (tb_task)
DROP TABLE IF EXISTS tb_task CASCADE;
CREATE TABLE tb_task (
    task_id SERIAL PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    task_status VARCHAR(20) DEFAULT 'CREATED',
    create_time TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    task_config JSONB,
    priority INTEGER DEFAULT 5,
    timeout_seconds INTEGER DEFAULT 3600,
    retry_count INTEGER DEFAULT 0,
    max_retry INTEGER DEFAULT 3,
    enabled BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE tb_task IS '任务表';
COMMENT ON COLUMN tb_task.task_id IS '任务ID';
COMMENT ON COLUMN tb_task.task_name IS '任务名称';
COMMENT ON COLUMN tb_task.task_type IS '任务类型：REALTIME_ANALYSIS, OFFLINE_ANALYSIS, DATA_EXPORT, PCAP_DOWNLOAD';
COMMENT ON COLUMN tb_task.task_status IS '任务状态：CREATED, RUNNING, COMPLETED, FAILED, CANCELLED';
COMMENT ON COLUMN tb_task.create_time IS '创建时间';
COMMENT ON COLUMN tb_task.task_config IS '任务配置';
COMMENT ON COLUMN tb_task.priority IS '优先级：1-最高，10-最低';
COMMENT ON COLUMN tb_task.timeout_seconds IS '超时时间（秒）';
COMMENT ON COLUMN tb_task.retry_count IS '重试次数';
COMMENT ON COLUMN tb_task.max_retry IS '最大重试次数';
COMMENT ON COLUMN tb_task.enabled IS '是否启用';
COMMENT ON COLUMN tb_task.created_by IS '创建人';
COMMENT ON COLUMN tb_task.updated_at IS '更新时间';

-- 任务批次表 (tb_task_batch)
DROP TABLE IF EXISTS tb_task_batch CASCADE;
CREATE TABLE tb_task_batch (
    batch_id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    batch_type VARCHAR(50) NOT NULL,
    fullflow_state INTEGER DEFAULT 0,
    begin_time BIGINT,
    end_time BIGINT,
    batch_bytes BIGINT DEFAULT 0,
    batch_status INTEGER DEFAULT 0,
    batch_config JSONB,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tb_task(task_id) ON DELETE CASCADE
);

COMMENT ON TABLE tb_task_batch IS '任务批次表';
COMMENT ON COLUMN tb_task_batch.batch_id IS '批次ID';
COMMENT ON COLUMN tb_task_batch.task_id IS '任务ID';
COMMENT ON COLUMN tb_task_batch.batch_type IS '批次类型：REALTIME, OFFLINE, EXPORT';
COMMENT ON COLUMN tb_task_batch.fullflow_state IS '全流程状态';
COMMENT ON COLUMN tb_task_batch.begin_time IS '开始时间（时间戳）';
COMMENT ON COLUMN tb_task_batch.end_time IS '结束时间（时间戳）';
COMMENT ON COLUMN tb_task_batch.batch_bytes IS '批次字节数';
COMMENT ON COLUMN tb_task_batch.batch_status IS '批次状态：0-待处理，1-处理中，2-已完成，-1-失败';
COMMENT ON COLUMN tb_task_batch.batch_config IS '批次配置';
COMMENT ON COLUMN tb_task_batch.create_time IS '创建时间';
COMMENT ON COLUMN tb_task_batch.update_time IS '更新时间';

-- 分析任务表 (tb_task_analysis)
DROP TABLE IF EXISTS tb_task_analysis CASCADE;
CREATE TABLE tb_task_analysis (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    net_flow VARCHAR(100),
    task_state INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_config JSONB,
    analysis_type VARCHAR(50),
    pcap_file_path VARCHAR(500),
    result_path VARCHAR(500),
    progress_percentage INTEGER DEFAULT 0,
    error_message TEXT,
    FOREIGN KEY (task_id) REFERENCES tb_task(task_id) ON DELETE CASCADE
);

COMMENT ON TABLE tb_task_analysis IS '分析任务表';
COMMENT ON COLUMN tb_task_analysis.id IS '主键';
COMMENT ON COLUMN tb_task_analysis.task_id IS '任务ID';
COMMENT ON COLUMN tb_task_analysis.task_name IS '任务名称';
COMMENT ON COLUMN tb_task_analysis.net_flow IS '网络流量';
COMMENT ON COLUMN tb_task_analysis.task_state IS '任务状态：0-待处理，1-处理中，2-已完成，-1-失败';
COMMENT ON COLUMN tb_task_analysis.create_time IS '创建时间';
COMMENT ON COLUMN tb_task_analysis.update_time IS '更新时间';
COMMENT ON COLUMN tb_task_analysis.task_config IS '任务配置';
COMMENT ON COLUMN tb_task_analysis.analysis_type IS '分析类型：REALTIME, OFFLINE';
COMMENT ON COLUMN tb_task_analysis.pcap_file_path IS 'PCAP文件路径';
COMMENT ON COLUMN tb_task_analysis.result_path IS '结果路径';
COMMENT ON COLUMN tb_task_analysis.progress_percentage IS '进度百分比';
COMMENT ON COLUMN tb_task_analysis.error_message IS '错误信息';

-- 统一下载任务表 (download_task)
DROP TABLE IF EXISTS download_task CASCADE;
CREATE TABLE download_task (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(100),
    task_uuid VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL DEFAULT 'pcap',
    source_type VARCHAR(50) DEFAULT 'session',
    source_service VARCHAR(50),
    
    -- 通用查询条件
    query_conditions JSONB,
    show_query TEXT,
    
    -- PCAP相关字段
    alarm_type VARCHAR(100),
    alarm_time TIMESTAMP WITH TIME ZONE,
    session_ids JSONB,
    pcap_file_paths JSONB,
    
    -- 文件处理相关
    file_format VARCHAR(20) DEFAULT 'pcap',
    compression_enabled BOOLEAN DEFAULT true,
    compression_type VARCHAR(32) DEFAULT 'zip',
    archive_file_path VARCHAR(1000),
    archive_file_name VARCHAR(500),
    archive_file_size BIGINT DEFAULT 0,
    
    -- 进度管理
    total_file_count INTEGER DEFAULT 0,
    processed_file_count INTEGER DEFAULT 0,
    total_records INTEGER DEFAULT 0,
    processed_records INTEGER DEFAULT 0,
    
    -- 状态管理
    status VARCHAR(32) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    error_message TEXT,
    
    -- 下载管理
    download_url VARCHAR(1000),
    download_count INTEGER DEFAULT 0,
    max_download_count INTEGER DEFAULT 10,
    
    -- 时间管理
    expire_time TIMESTAMP WITH TIME ZONE,
    retention_hours INTEGER DEFAULT 24,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    start_time TIMESTAMP WITH TIME ZONE,
    complete_time TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (task_id) REFERENCES tb_task(task_id) ON DELETE SET NULL
);

COMMENT ON TABLE download_task IS '统一下载任务表';
COMMENT ON COLUMN download_task.id IS '主键';
COMMENT ON COLUMN download_task.task_id IS '任务ID（关联tb_task表）';
COMMENT ON COLUMN download_task.task_uuid IS '任务唯一标识UUID';
COMMENT ON COLUMN download_task.user_id IS '创建者用户ID';
COMMENT ON COLUMN download_task.task_type IS '任务类型：pcap, data_export, file_download';
COMMENT ON COLUMN download_task.source_type IS '来源类型：session, alarm, custom, manual';
COMMENT ON COLUMN download_task.source_service IS '来源服务名称';
COMMENT ON COLUMN download_task.query_conditions IS '查询条件（JSONB格式）';
COMMENT ON COLUMN download_task.show_query IS '显示用查询条件';
COMMENT ON COLUMN download_task.alarm_type IS '告警类型（PCAP任务专用）';
COMMENT ON COLUMN download_task.alarm_time IS '告警时间（PCAP任务专用）';
COMMENT ON COLUMN download_task.session_ids IS '会话ID列表（JSONB格式，PCAP任务专用）';
COMMENT ON COLUMN download_task.pcap_file_paths IS 'PCAP文件路径列表（JSONB格式，PCAP任务专用）';
COMMENT ON COLUMN download_task.file_format IS '文件格式：pcap, pcapng, csv, json, excel';
COMMENT ON COLUMN download_task.compression_enabled IS '是否启用压缩';
COMMENT ON COLUMN download_task.compression_type IS '压缩类型：zip, tar.gz, 7z';
COMMENT ON COLUMN download_task.archive_file_path IS '压缩包文件路径';
COMMENT ON COLUMN download_task.archive_file_name IS '压缩包文件名';
COMMENT ON COLUMN download_task.archive_file_size IS '压缩包文件大小（字节）';
COMMENT ON COLUMN download_task.total_file_count IS '总文件数量';
COMMENT ON COLUMN download_task.processed_file_count IS '已处理文件数量';
COMMENT ON COLUMN download_task.total_records IS '总记录数（数据导出任务专用）';
COMMENT ON COLUMN download_task.processed_records IS '已处理记录数（数据导出任务专用）';
COMMENT ON COLUMN download_task.status IS '任务状态：pending, processing, completed, failed, expired, cancelled';
COMMENT ON COLUMN download_task.progress IS '进度百分比 (0-100)';
COMMENT ON COLUMN download_task.error_message IS '错误信息';
COMMENT ON COLUMN download_task.download_url IS '下载URL';
COMMENT ON COLUMN download_task.download_count IS '已下载次数';
COMMENT ON COLUMN download_task.max_download_count IS '最大下载次数';
COMMENT ON COLUMN download_task.expire_time IS '文件过期时间';
COMMENT ON COLUMN download_task.retention_hours IS '文件保留小时数';
COMMENT ON COLUMN download_task.create_time IS '创建时间';
COMMENT ON COLUMN download_task.update_time IS '更新时间';
COMMENT ON COLUMN download_task.start_time IS '开始处理时间';
COMMENT ON COLUMN download_task.complete_time IS '完成时间';

-- 下载任务历史记录表 (download_task_history)
DROP TABLE IF EXISTS download_task_history CASCADE;
CREATE TABLE download_task_history (
    id SERIAL PRIMARY KEY,
    task_uuid VARCHAR(100) NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    source_type VARCHAR(50),
    query_conditions JSONB,
    file_format VARCHAR(20),
    archive_file_size BIGINT,
    download_count INTEGER DEFAULT 0,
    status VARCHAR(32),
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    complete_time TIMESTAMP WITH TIME ZONE,
    delete_time TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (task_uuid) REFERENCES download_task(task_uuid) ON DELETE CASCADE
);

COMMENT ON TABLE download_task_history IS '下载任务历史记录表';
COMMENT ON COLUMN download_task_history.id IS '主键';
COMMENT ON COLUMN download_task_history.task_uuid IS '任务UUID';
COMMENT ON COLUMN download_task_history.user_id IS '用户ID';
COMMENT ON COLUMN download_task_history.task_type IS '任务类型';
COMMENT ON COLUMN download_task_history.source_type IS '来源类型';
COMMENT ON COLUMN download_task_history.query_conditions IS '查询条件';
COMMENT ON COLUMN download_task_history.file_format IS '文件格式';
COMMENT ON COLUMN download_task_history.archive_file_size IS '文件大小';
COMMENT ON COLUMN download_task_history.download_count IS '下载次数';
COMMENT ON COLUMN download_task_history.status IS '最终状态';
COMMENT ON COLUMN download_task_history.create_time IS '创建时间';
COMMENT ON COLUMN download_task_history.complete_time IS '完成时间';
COMMENT ON COLUMN download_task_history.delete_time IS '删除时间';



-- ========================================
-- 索引创建
-- ========================================

-- 任务表索引
CREATE INDEX idx_tb_task_name ON tb_task(task_name);
CREATE INDEX idx_tb_task_type ON tb_task(task_type);
CREATE INDEX idx_tb_task_status ON tb_task(task_status);
CREATE INDEX idx_tb_task_enabled ON tb_task(enabled);
CREATE INDEX idx_tb_task_priority ON tb_task(priority);
CREATE INDEX idx_tb_task_create_time ON tb_task(create_time);

-- 任务批次表索引
CREATE INDEX idx_tb_task_batch_task_id ON tb_task_batch(task_id);
CREATE INDEX idx_tb_task_batch_type ON tb_task_batch(batch_type);
CREATE INDEX idx_tb_task_batch_status ON tb_task_batch(batch_status);
CREATE INDEX idx_tb_task_batch_create_time ON tb_task_batch(create_time);

-- 分析任务表索引
CREATE INDEX idx_tb_task_analysis_task_id ON tb_task_analysis(task_id);
CREATE INDEX idx_tb_task_analysis_state ON tb_task_analysis(task_state);
CREATE INDEX idx_tb_task_analysis_type ON tb_task_analysis(analysis_type);
CREATE INDEX idx_tb_task_analysis_create_time ON tb_task_analysis(create_time);

-- 统一下载任务表索引
CREATE INDEX idx_download_task_task_id ON download_task(task_id);
CREATE INDEX idx_download_task_task_uuid ON download_task(task_uuid);
CREATE INDEX idx_download_task_user_id ON download_task(user_id);
CREATE INDEX idx_download_task_task_type ON download_task(task_type);
CREATE INDEX idx_download_task_source_type ON download_task(source_type);
CREATE INDEX idx_download_task_source_service ON download_task(source_service);
CREATE INDEX idx_download_task_status ON download_task(status);
CREATE INDEX idx_download_task_create_time ON download_task(create_time);
CREATE INDEX idx_download_task_expire_time ON download_task(expire_time);
CREATE INDEX idx_download_task_session_ids ON download_task USING GIN(session_ids);
CREATE INDEX idx_download_task_query_conditions ON download_task USING GIN(query_conditions);

-- 下载任务历史记录表索引
CREATE INDEX idx_download_task_history_task_uuid ON download_task_history(task_uuid);
CREATE INDEX idx_download_task_history_user_id ON download_task_history(user_id);
CREATE INDEX idx_download_task_history_task_type ON download_task_history(task_type);
CREATE INDEX idx_download_task_history_create_time ON download_task_history(create_time);



-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建update_time字段更新触发器函数
CREATE OR REPLACE FUNCTION update_update_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_tb_task_updated_at BEFORE UPDATE ON tb_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tb_task_batch_update_time BEFORE UPDATE ON tb_task_batch
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tb_task_analysis_update_time BEFORE UPDATE ON tb_task_analysis
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_task_update_time BEFORE UPDATE ON download_task
    FOR EACH ROW EXECUTE FUNCTION update_update_time_column();



-- ========================================
-- 初始化数据
-- ========================================

-- 插入示例任务
INSERT INTO tb_task (task_name, task_type, task_status, task_config, created_by) VALUES
('系统日志清理', 'REALTIME_ANALYSIS', 'CREATED', 
'{"log_retention_days": 30, "log_path": "/var/log/nta"}', 'system'),
('数据库备份', 'OFFLINE_ANALYSIS', 'CREATED', 
'{"backup_path": "/backup/db", "compression": true}', 'system'),
('性能报告生成', 'DATA_EXPORT', 'CREATED', 
'{"report_type": "performance", "format": "PDF"}', 'system');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE tb_task REPLICA IDENTITY FULL;
ALTER TABLE tb_task_batch REPLICA IDENTITY FULL;
ALTER TABLE tb_task_analysis REPLICA IDENTITY FULL;
ALTER TABLE download_task REPLICA IDENTITY FULL;
ALTER TABLE download_task_history REPLICA IDENTITY FULL;
ALTER TABLE task_dependency REPLICA IDENTITY FULL;