-- ========================================
-- Task Service Database Schema
-- 任务服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE task_service;
-- \c task_service;

-- ========================================
-- 核心任务管理表
-- ========================================

-- 任务表 (tb_task)
DROP TABLE IF EXISTS tb_task CASCADE;
CREATE TABLE tb_task (
    task_id SERIAL PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    task_status VARCHAR(20) DEFAULT 'CREATED',
    create_time TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    task_config JSONB,
    priority INTEGER DEFAULT 5,
    timeout_seconds INTEGER DEFAULT 3600,
    retry_count INTEGER DEFAULT 0,
    max_retry INTEGER DEFAULT 3,
    enabled BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE tb_task IS '任务表';
COMMENT ON COLUMN tb_task.task_id IS '任务ID';
COMMENT ON COLUMN tb_task.task_name IS '任务名称';
COMMENT ON COLUMN tb_task.task_type IS '任务类型：REALTIME_ANALYSIS, OFFLINE_ANALYSIS, DATA_EXPORT, PCAP_DOWNLOAD';
COMMENT ON COLUMN tb_task.task_status IS '任务状态：CREATED, RUNNING, COMPLETED, FAILED, CANCELLED';
COMMENT ON COLUMN tb_task.create_time IS '创建时间';
COMMENT ON COLUMN tb_task.task_config IS '任务配置';
COMMENT ON COLUMN tb_task.priority IS '优先级：1-最高，10-最低';
COMMENT ON COLUMN tb_task.timeout_seconds IS '超时时间（秒）';
COMMENT ON COLUMN tb_task.retry_count IS '重试次数';
COMMENT ON COLUMN tb_task.max_retry IS '最大重试次数';
COMMENT ON COLUMN tb_task.enabled IS '是否启用';
COMMENT ON COLUMN tb_task.created_by IS '创建人';
COMMENT ON COLUMN tb_task.updated_at IS '更新时间';

-- 任务批次表 (tb_task_batch)
DROP TABLE IF EXISTS tb_task_batch CASCADE;
CREATE TABLE tb_task_batch (
    batch_id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    batch_type VARCHAR(50) NOT NULL,
    fullflow_state INTEGER DEFAULT 0,
    begin_time BIGINT,
    end_time BIGINT,
    batch_bytes BIGINT DEFAULT 0,
    batch_status INTEGER DEFAULT 0,
    batch_config JSONB,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tb_task(task_id) ON DELETE CASCADE
);

COMMENT ON TABLE tb_task_batch IS '任务批次表';
COMMENT ON COLUMN tb_task_batch.batch_id IS '批次ID';
COMMENT ON COLUMN tb_task_batch.task_id IS '任务ID';
COMMENT ON COLUMN tb_task_batch.batch_type IS '批次类型：REALTIME, OFFLINE, EXPORT';
COMMENT ON COLUMN tb_task_batch.fullflow_state IS '全流程状态';
COMMENT ON COLUMN tb_task_batch.begin_time IS '开始时间（时间戳）';
COMMENT ON COLUMN tb_task_batch.end_time IS '结束时间（时间戳）';
COMMENT ON COLUMN tb_task_batch.batch_bytes IS '批次字节数';
COMMENT ON COLUMN tb_task_batch.batch_status IS '批次状态：0-待处理，1-处理中，2-已完成，-1-失败';
COMMENT ON COLUMN tb_task_batch.batch_config IS '批次配置';
COMMENT ON COLUMN tb_task_batch.create_time IS '创建时间';
COMMENT ON COLUMN tb_task_batch.update_time IS '更新时间';

-- 分析任务表 (tb_task_analysis)
DROP TABLE IF EXISTS tb_task_analysis CASCADE;
CREATE TABLE tb_task_analysis (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    net_flow VARCHAR(100),
    task_state INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_config JSONB,
    analysis_type VARCHAR(50),
    pcap_file_path VARCHAR(500),
    result_path VARCHAR(500),
    progress_percentage INTEGER DEFAULT 0,
    error_message TEXT,
    FOREIGN KEY (task_id) REFERENCES tb_task(task_id) ON DELETE CASCADE
);

COMMENT ON TABLE tb_task_analysis IS '分析任务表';
COMMENT ON COLUMN tb_task_analysis.id IS '主键';
COMMENT ON COLUMN tb_task_analysis.task_id IS '任务ID';
COMMENT ON COLUMN tb_task_analysis.task_name IS '任务名称';
COMMENT ON COLUMN tb_task_analysis.net_flow IS '网络流量';
COMMENT ON COLUMN tb_task_analysis.task_state IS '任务状态：0-待处理，1-处理中，2-已完成，-1-失败';
COMMENT ON COLUMN tb_task_analysis.create_time IS '创建时间';
COMMENT ON COLUMN tb_task_analysis.update_time IS '更新时间';
COMMENT ON COLUMN tb_task_analysis.task_config IS '任务配置';
COMMENT ON COLUMN tb_task_analysis.analysis_type IS '分析类型：REALTIME, OFFLINE';
COMMENT ON COLUMN tb_task_analysis.pcap_file_path IS 'PCAP文件路径';
COMMENT ON COLUMN tb_task_analysis.result_path IS '结果路径';
COMMENT ON COLUMN tb_task_analysis.progress_percentage IS '进度百分比';
COMMENT ON COLUMN tb_task_analysis.error_message IS '错误信息';

-- PCAP下载任务表 (pcap_download_task)
DROP TABLE IF EXISTS pcap_download_task CASCADE;
CREATE TABLE pcap_download_task (
    id SERIAL PRIMARY KEY,
    task_id INTEGER,
    task_uuid VARCHAR(64) UNIQUE NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    source_type VARCHAR(50) DEFAULT 'session',
    source_service VARCHAR(50),
    alarm_type VARCHAR(100),
    alarm_time BIGINT,
    session_ids JSONB NOT NULL,
    query_conditions JSONB,
    pcap_file_paths JSONB,
    file_format VARCHAR(32) DEFAULT 'pcap',
    compression_enabled BOOLEAN DEFAULT true,
    compression_type VARCHAR(32) DEFAULT 'zip',
    archive_file_path VARCHAR(1000),
    archive_file_name VARCHAR(200),
    archive_file_size BIGINT DEFAULT 0,
    total_file_count INTEGER DEFAULT 0,
    processed_file_count INTEGER DEFAULT 0,
    status VARCHAR(32) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    error_message TEXT,
    download_url VARCHAR(1000),
    download_count INTEGER DEFAULT 0,
    max_download_count INTEGER DEFAULT 10,
    expire_time TIMESTAMP WITH TIME ZONE,
    retention_hours INTEGER DEFAULT 24,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    start_time TIMESTAMP WITH TIME ZONE,
    complete_time TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (task_id) REFERENCES tb_task(task_id) ON DELETE SET NULL
);

COMMENT ON TABLE pcap_download_task IS 'PCAP下载任务表';
COMMENT ON COLUMN pcap_download_task.id IS '主键';
COMMENT ON COLUMN pcap_download_task.task_id IS '任务ID（关联tb_task表）';
COMMENT ON COLUMN pcap_download_task.task_uuid IS '任务唯一标识UUID';
COMMENT ON COLUMN pcap_download_task.user_id IS '创建者用户ID';
COMMENT ON COLUMN pcap_download_task.source_type IS '来源类型：session, alarm, custom';
COMMENT ON COLUMN pcap_download_task.source_service IS '来源服务名称';
COMMENT ON COLUMN pcap_download_task.alarm_type IS '告警类型';
COMMENT ON COLUMN pcap_download_task.alarm_time IS '告警时间';
COMMENT ON COLUMN pcap_download_task.session_ids IS '会话ID列表（JSONB格式）';
COMMENT ON COLUMN pcap_download_task.query_conditions IS '查询条件（JSONB格式）';
COMMENT ON COLUMN pcap_download_task.pcap_file_paths IS 'PCAP文件路径列表（JSONB格式）';
COMMENT ON COLUMN pcap_download_task.file_format IS '文件格式：pcap, pcapng';
COMMENT ON COLUMN pcap_download_task.compression_enabled IS '是否启用压缩';
COMMENT ON COLUMN pcap_download_task.compression_type IS '压缩类型：zip, tar.gz, 7z';
COMMENT ON COLUMN pcap_download_task.archive_file_path IS '压缩包文件路径';
COMMENT ON COLUMN pcap_download_task.archive_file_name IS '压缩包文件名';
COMMENT ON COLUMN pcap_download_task.archive_file_size IS '压缩包文件大小（字节）';
COMMENT ON COLUMN pcap_download_task.total_file_count IS '总文件数量';
COMMENT ON COLUMN pcap_download_task.processed_file_count IS '已处理文件数量';
COMMENT ON COLUMN pcap_download_task.status IS '任务状态：pending, processing, completed, failed, expired, cancelled';
COMMENT ON COLUMN pcap_download_task.progress IS '进度百分比 (0-100)';
COMMENT ON COLUMN pcap_download_task.error_message IS '错误信息';
COMMENT ON COLUMN pcap_download_task.download_url IS '下载URL';
COMMENT ON COLUMN pcap_download_task.download_count IS '已下载次数';
COMMENT ON COLUMN pcap_download_task.max_download_count IS '最大下载次数';
COMMENT ON COLUMN pcap_download_task.expire_time IS '文件过期时间';
COMMENT ON COLUMN pcap_download_task.retention_hours IS '文件保留小时数';
COMMENT ON COLUMN pcap_download_task.create_time IS '创建时间';
COMMENT ON COLUMN pcap_download_task.update_time IS '更新时间';
COMMENT ON COLUMN pcap_download_task.start_time IS '开始处理时间';
COMMENT ON COLUMN pcap_download_task.complete_time IS '完成时间';

-- 数据导出任务表 (data_export_task)
DROP TABLE IF EXISTS data_export_task CASCADE;
CREATE TABLE data_export_task (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(100),
    export_type VARCHAR(50),
    query_condition TEXT,
    export_format VARCHAR(20),
    file_path VARCHAR(1000),
    file_size BIGINT,
    total_records INTEGER DEFAULT 0,
    exported_records INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    progress INTEGER DEFAULT 0,
    error_message TEXT,
    download_url VARCHAR(1000),
    expire_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    start_time TIMESTAMP,
    complete_time TIMESTAMP
);

COMMENT ON TABLE data_export_task IS '数据导出任务表';
COMMENT ON COLUMN data_export_task.id IS '主键';
COMMENT ON COLUMN data_export_task.task_id IS '任务ID';
COMMENT ON COLUMN data_export_task.user_id IS '创建者用户ID';
COMMENT ON COLUMN data_export_task.export_type IS '导出类型';
COMMENT ON COLUMN data_export_task.query_condition IS '查询条件';
COMMENT ON COLUMN data_export_task.export_format IS '导出格式：CSV, JSON, EXCEL等';
COMMENT ON COLUMN data_export_task.file_path IS '导出文件路径';
COMMENT ON COLUMN data_export_task.file_size IS '文件大小（字节）';
COMMENT ON COLUMN data_export_task.total_records IS '总记录数';
COMMENT ON COLUMN data_export_task.exported_records IS '已导出记录数';
COMMENT ON COLUMN data_export_task.status IS '任务状态：1-待处理，2-处理中，3-已完成，-1-失败';
COMMENT ON COLUMN data_export_task.progress IS '进度百分比 (0-100)';
COMMENT ON COLUMN data_export_task.error_message IS '错误信息';
COMMENT ON COLUMN data_export_task.download_url IS '下载URL';
COMMENT ON COLUMN data_export_task.expire_time IS '文件过期时间';
COMMENT ON COLUMN data_export_task.create_time IS '创建时间';
COMMENT ON COLUMN data_export_task.update_time IS '更新时间';
COMMENT ON COLUMN data_export_task.start_time IS '开始处理时间';
COMMENT ON COLUMN data_export_task.complete_time IS '完成时间';

-- 数据导出任务注册表 (data_export_task_register)
DROP TABLE IF EXISTS data_export_task_register CASCADE;
CREATE TABLE data_export_task_register (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100),
    export_type VARCHAR(50),
    query_condition TEXT,
    export_format VARCHAR(20),
    download_count INTEGER DEFAULT 0,
    delete_time TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type VARCHAR(50),
    error_msg TEXT,
    status INTEGER DEFAULT 1,
    show_query TEXT
);

COMMENT ON TABLE data_export_task_register IS '数据导出任务注册表';
COMMENT ON COLUMN data_export_task_register.id IS '主键';
COMMENT ON COLUMN data_export_task_register.user_id IS '用户ID';
COMMENT ON COLUMN data_export_task_register.export_type IS '导出类型';
COMMENT ON COLUMN data_export_task_register.query_condition IS '查询条件';
COMMENT ON COLUMN data_export_task_register.export_format IS '导出格式';
COMMENT ON COLUMN data_export_task_register.download_count IS '下载次数';
COMMENT ON COLUMN data_export_task_register.delete_time IS '删除时间';
COMMENT ON COLUMN data_export_task_register.update_time IS '更新时间';
COMMENT ON COLUMN data_export_task_register.create_time IS '创建时间';
COMMENT ON COLUMN data_export_task_register.task_type IS '任务类型';
COMMENT ON COLUMN data_export_task_register.error_msg IS '错误信息';
COMMENT ON COLUMN data_export_task_register.status IS '状态';
COMMENT ON COLUMN data_export_task_register.show_query IS '显示查询条件';

-- 下载任务表 (tb_download_task)
DROP TABLE IF EXISTS tb_download_task CASCADE;
CREATE TABLE tb_download_task (
    id SERIAL PRIMARY KEY,
    created_by VARCHAR(100),
    path VARCHAR(1000),
    query TEXT,
    show_query TEXT,
    type VARCHAR(50),
    session_id VARCHAR(100),
    state INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status INTEGER DEFAULT 1,
    task_id VARCHAR(100),
    download_count INTEGER DEFAULT 0,
    delete_time TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at_standard TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type VARCHAR(50),
    error_msg TEXT
);

COMMENT ON TABLE tb_download_task IS '下载任务表';
COMMENT ON COLUMN tb_download_task.id IS '主键';
COMMENT ON COLUMN tb_download_task.created_by IS '创建者';
COMMENT ON COLUMN tb_download_task.path IS '文件路径';
COMMENT ON COLUMN tb_download_task.query IS '查询条件';
COMMENT ON COLUMN tb_download_task.show_query IS '显示查询条件';
COMMENT ON COLUMN tb_download_task.type IS '下载类型';
COMMENT ON COLUMN tb_download_task.session_id IS '会话ID';
COMMENT ON COLUMN tb_download_task.state IS '状态';
COMMENT ON COLUMN tb_download_task.created_at IS '创建时间';
COMMENT ON COLUMN tb_download_task.end_time IS '结束时间';
COMMENT ON COLUMN tb_download_task.status IS '任务状态';
COMMENT ON COLUMN tb_download_task.task_id IS '任务ID';
COMMENT ON COLUMN tb_download_task.download_count IS '下载次数';
COMMENT ON COLUMN tb_download_task.delete_time IS '删除时间';
COMMENT ON COLUMN tb_download_task.updated_at IS '更新时间';
COMMENT ON COLUMN tb_download_task.created_at_standard IS '标准创建时间';
COMMENT ON COLUMN tb_download_task.task_type IS '任务类型';
COMMENT ON COLUMN tb_download_task.error_msg IS '错误信息';

-- 下载任务注册表 (tb_download_task_register)
DROP TABLE IF EXISTS tb_download_task_register CASCADE;
CREATE TABLE tb_download_task_register (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100),
    path VARCHAR(1000),
    query TEXT,
    type VARCHAR(50),
    download_count INTEGER DEFAULT 0,
    delete_time TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type VARCHAR(50),
    error_msg TEXT,
    status INTEGER DEFAULT 1,
    show_query TEXT
);

COMMENT ON TABLE tb_download_task_register IS '下载任务注册表';
COMMENT ON COLUMN tb_download_task_register.id IS '主键';
COMMENT ON COLUMN tb_download_task_register.user_id IS '用户ID';
COMMENT ON COLUMN tb_download_task_register.path IS '文件路径';
COMMENT ON COLUMN tb_download_task_register.query IS '查询条件';
COMMENT ON COLUMN tb_download_task_register.type IS '下载类型';
COMMENT ON COLUMN tb_download_task_register.download_count IS '下载次数';
COMMENT ON COLUMN tb_download_task_register.delete_time IS '删除时间';
COMMENT ON COLUMN tb_download_task_register.update_time IS '更新时间';
COMMENT ON COLUMN tb_download_task_register.create_time IS '创建时间';
COMMENT ON COLUMN tb_download_task_register.task_type IS '任务类型';
COMMENT ON COLUMN tb_download_task_register.error_msg IS '错误信息';
COMMENT ON COLUMN tb_download_task_register.status IS '状态';
COMMENT ON COLUMN tb_download_task_register.show_query IS '显示查询条件';



-- ========================================
-- 索引创建
-- ========================================

-- 任务表索引
CREATE INDEX idx_tb_task_name ON tb_task(task_name);
CREATE INDEX idx_tb_task_type ON tb_task(task_type);
CREATE INDEX idx_tb_task_status ON tb_task(task_status);
CREATE INDEX idx_tb_task_enabled ON tb_task(enabled);
CREATE INDEX idx_tb_task_priority ON tb_task(priority);
CREATE INDEX idx_tb_task_create_time ON tb_task(create_time);

-- 任务批次表索引
CREATE INDEX idx_tb_task_batch_task_id ON tb_task_batch(task_id);
CREATE INDEX idx_tb_task_batch_type ON tb_task_batch(batch_type);
CREATE INDEX idx_tb_task_batch_status ON tb_task_batch(batch_status);
CREATE INDEX idx_tb_task_batch_create_time ON tb_task_batch(create_time);

-- 分析任务表索引
CREATE INDEX idx_tb_task_analysis_task_id ON tb_task_analysis(task_id);
CREATE INDEX idx_tb_task_analysis_state ON tb_task_analysis(task_state);
CREATE INDEX idx_tb_task_analysis_type ON tb_task_analysis(analysis_type);
CREATE INDEX idx_tb_task_analysis_create_time ON tb_task_analysis(create_time);

-- PCAP下载任务表索引
CREATE INDEX idx_pcap_download_task_task_id ON pcap_download_task(task_id);
CREATE INDEX idx_pcap_download_task_task_uuid ON pcap_download_task(task_uuid);
CREATE INDEX idx_pcap_download_task_user_id ON pcap_download_task(user_id);
CREATE INDEX idx_pcap_download_task_source_type ON pcap_download_task(source_type);
CREATE INDEX idx_pcap_download_task_source_service ON pcap_download_task(source_service);
CREATE INDEX idx_pcap_download_task_status ON pcap_download_task(status);
CREATE INDEX idx_pcap_download_task_create_time ON pcap_download_task(create_time);
CREATE INDEX idx_pcap_download_task_expire_time ON pcap_download_task(expire_time);
CREATE INDEX idx_pcap_download_task_session_ids ON pcap_download_task USING GIN(session_ids);
CREATE INDEX idx_pcap_download_task_query_conditions ON pcap_download_task USING GIN(query_conditions);

-- 数据导出任务表索引
CREATE INDEX idx_data_export_task_task_id ON data_export_task(task_id);
CREATE INDEX idx_data_export_task_user_id ON data_export_task(user_id);
CREATE INDEX idx_data_export_task_status ON data_export_task(status);
CREATE INDEX idx_data_export_task_create_time ON data_export_task(create_time);

-- 数据导出任务注册表索引
CREATE INDEX idx_data_export_task_register_user_id ON data_export_task_register(user_id);
CREATE INDEX idx_data_export_task_register_status ON data_export_task_register(status);
CREATE INDEX idx_data_export_task_register_create_time ON data_export_task_register(create_time);

-- 下载任务表索引
CREATE INDEX idx_tb_download_task_created_by ON tb_download_task(created_by);
CREATE INDEX idx_tb_download_task_status ON tb_download_task(status);
CREATE INDEX idx_tb_download_task_created_at ON tb_download_task(created_at);

-- 下载任务注册表索引
CREATE INDEX idx_tb_download_task_register_user_id ON tb_download_task_register(user_id);
CREATE INDEX idx_tb_download_task_register_status ON tb_download_task_register(status);
CREATE INDEX idx_tb_download_task_register_create_time ON tb_download_task_register(create_time);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建update_time字段更新触发器函数
CREATE OR REPLACE FUNCTION update_update_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_tb_task_updated_at BEFORE UPDATE ON tb_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tb_task_batch_update_time BEFORE UPDATE ON tb_task_batch
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tb_task_analysis_update_time BEFORE UPDATE ON tb_task_analysis
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pcap_download_task_update_time BEFORE UPDATE ON pcap_download_task
    FOR EACH ROW EXECUTE FUNCTION update_update_time_column();

CREATE TRIGGER update_data_export_task_update_time BEFORE UPDATE ON data_export_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_export_task_register_update_time BEFORE UPDATE ON data_export_task_register
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tb_download_task_updated_at BEFORE UPDATE ON tb_download_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tb_download_task_register_update_time BEFORE UPDATE ON tb_download_task_register
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入示例任务
INSERT INTO tb_task (task_name, task_type, task_status, task_config, created_by) VALUES
('系统日志清理', 'REALTIME_ANALYSIS', 'CREATED', 
'{"log_retention_days": 30, "log_path": "/var/log/nta"}', 'system'),
('数据库备份', 'OFFLINE_ANALYSIS', 'CREATED', 
'{"backup_path": "/backup/db", "compression": true}', 'system'),
('性能报告生成', 'DATA_EXPORT', 'CREATED', 
'{"report_type": "performance", "format": "PDF"}', 'system');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE tb_task REPLICA IDENTITY FULL;
ALTER TABLE tb_task_batch REPLICA IDENTITY FULL;
ALTER TABLE tb_task_analysis REPLICA IDENTITY FULL;
ALTER TABLE pcap_download_task REPLICA IDENTITY FULL;
ALTER TABLE data_export_task REPLICA IDENTITY FULL;
ALTER TABLE data_export_task_register REPLICA IDENTITY FULL;
ALTER TABLE tb_download_task REPLICA IDENTITY FULL;
ALTER TABLE tb_download_task_register REPLICA IDENTITY FULL;
ALTER TABLE task_dependency REPLICA IDENTITY FULL;