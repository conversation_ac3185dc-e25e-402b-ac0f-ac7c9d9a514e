-- ========================================
-- 证书服务相关表
-- ========================================

-- 证书基础信息表（对应Certificate.java实体）
DROP TABLE IF EXISTS cert CASCADE;

CREATE TABLE cert (
    cert_id VARCHAR(64) PRIMARY KEY,
    first_seen TIMESTAMP,
    last_seen TIMESTAMP,
    threat_level VARCHAR(20),
    trust_level VARCHAR(20),
    remark TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cert IS '证书基础信息表';
COMMENT ON COLUMN cert.cert_id IS '证书ID（SHA1哈希值）';
COMMENT ON COLUMN cert.first_seen IS '首次发现时间';
COMMENT ON COLUMN cert.last_seen IS '最后发现时间';
COMMENT ON COLUMN cert.threat_level IS '威胁等级: LOW, MEDIUM, HIGH, CRITICAL';
COMMENT ON COLUMN cert.trust_level IS '信任等级: LOW, MEDIUM, HIGH';
COMMENT ON COLUMN cert.remark IS '备注信息';
COMMENT ON COLUMN cert.created_at IS '创建时间';
COMMENT ON COLUMN cert.updated_at IS '更新时间';

-- 证书详细信息表（对应DimCertificate.java实体）
DROP TABLE IF EXISTS dim_cert CASCADE;

CREATE TABLE dim_cert (
    cert_id VARCHAR(64) PRIMARY KEY,
    der_sha1 VARCHAR(64),
    der_md5 VARCHAR(64),
    der_sha256 VARCHAR(64),
    pem_sha1 VARCHAR(64),
    pem_md5 VARCHAR(64),
    pem_sha256 VARCHAR(64),
    version INTEGER,
    serial_number TEXT,
    format VARCHAR(10),
    issuer_id VARCHAR(64),
    subject_id VARCHAR(64),
    not_before TIMESTAMP,
    not_after TIMESTAMP,
    common_name TEXT,
    subject_alternative_names TEXT[],
    issuer_alternative_names TEXT[],
    public_key_algorithm VARCHAR(50),
    public_key_size INTEGER,
    public_key_parameter TEXT,
    spki_sha256 VARCHAR(64),
    signature_algorithm VARCHAR(100),
    signature_algorithm_oid VARCHAR(100),
    signature_hash_algorithm VARCHAR(50),
    key_usage TEXT[],
    extended_key_usage TEXT[],
    basic_constraints TEXT,
    authority_key_identifier VARCHAR(128),
    subject_key_identifier VARCHAR(128),
    crl_distribution_points TEXT[],
    authority_info_access TEXT,
    subject_info_access TEXT,
    cert_policies TEXT,
    source VARCHAR(50),
    user_type VARCHAR(20),
    business_type VARCHAR(20),
    ca_type VARCHAR(20),
    industry_type VARCHAR(50),
    subject_area VARCHAR(100),
    issuer_area VARCHAR(100),
    is_trusted BOOLEAN DEFAULT FALSE,
    is_parsed_successfully BOOLEAN DEFAULT TRUE,
    is_corrupted BOOLEAN DEFAULT FALSE,
    cert_occurrence_count INTEGER DEFAULT 0,
    threat_score DOUBLE PRECISION DEFAULT 0.0,
    trust_score DOUBLE PRECISION DEFAULT 0.0,
    threat_level VARCHAR(20),
    labels TEXT[],
    associated_domains TEXT[],
    associated_ips TEXT[],
    organization TEXT,
    first_seen TIMESTAMP,
    last_seen TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark TEXT
);

COMMENT ON TABLE dim_cert IS '证书详细信息表';
COMMENT ON COLUMN dim_cert.cert_id IS '证书ID（SHA1哈希值）';
COMMENT ON COLUMN dim_cert.der_sha1 IS 'DER格式SHA1哈希';
COMMENT ON COLUMN dim_cert.der_md5 IS 'DER格式MD5哈希';
COMMENT ON COLUMN dim_cert.der_sha256 IS 'DER格式SHA256哈希';
COMMENT ON COLUMN dim_cert.pem_sha1 IS 'PEM格式SHA1哈希';
COMMENT ON COLUMN dim_cert.pem_md5 IS 'PEM格式MD5哈希';
COMMENT ON COLUMN dim_cert.pem_sha256 IS 'PEM格式SHA256哈希';
COMMENT ON COLUMN dim_cert.version IS '证书版本';
COMMENT ON COLUMN dim_cert.serial_number IS '证书序列号';
COMMENT ON COLUMN dim_cert.format IS '证书格式';
COMMENT ON COLUMN dim_cert.issuer_id IS '颁发者ID';
COMMENT ON COLUMN dim_cert.subject_id IS '主体ID';
COMMENT ON COLUMN dim_cert.not_before IS '有效期开始时间';
COMMENT ON COLUMN dim_cert.not_after IS '有效期结束时间';
COMMENT ON COLUMN dim_cert.common_name IS '通用名称';
COMMENT ON COLUMN dim_cert.subject_alternative_names IS '主体备用名称';
COMMENT ON COLUMN dim_cert.issuer_alternative_names IS '颁发者备用名称';
COMMENT ON COLUMN dim_cert.public_key_algorithm IS '公钥算法';
COMMENT ON COLUMN dim_cert.public_key_size IS '公钥长度';
COMMENT ON COLUMN dim_cert.public_key_parameter IS '公钥参数';
COMMENT ON COLUMN dim_cert.spki_sha256 IS '主体公钥信息SHA256';
COMMENT ON COLUMN dim_cert.signature_algorithm IS '签名算法';
COMMENT ON COLUMN dim_cert.signature_algorithm_oid IS '签名算法OID';
COMMENT ON COLUMN dim_cert.signature_hash_algorithm IS '签名哈希算法';
COMMENT ON COLUMN dim_cert.key_usage IS '密钥用途';
COMMENT ON COLUMN dim_cert.extended_key_usage IS '扩展密钥用途';
COMMENT ON COLUMN dim_cert.basic_constraints IS '基本约束';
COMMENT ON COLUMN dim_cert.authority_key_identifier IS '授权密钥标识符';
COMMENT ON COLUMN dim_cert.subject_key_identifier IS '主体密钥标识符';
COMMENT ON COLUMN dim_cert.crl_distribution_points IS 'CRL分发点';
COMMENT ON COLUMN dim_cert.authority_info_access IS '授权信息访问';
COMMENT ON COLUMN dim_cert.subject_info_access IS '主体信息访问';
COMMENT ON COLUMN dim_cert.cert_policies IS '证书策略';
COMMENT ON COLUMN dim_cert.source IS '证书来源';
COMMENT ON COLUMN dim_cert.user_type IS '用户类型';
COMMENT ON COLUMN dim_cert.business_type IS '业务类型';
COMMENT ON COLUMN dim_cert.ca_type IS 'CA类型';
COMMENT ON COLUMN dim_cert.industry_type IS '行业类型';
COMMENT ON COLUMN dim_cert.subject_area IS '主体地区';
COMMENT ON COLUMN dim_cert.issuer_area IS '颁发者地区';
COMMENT ON COLUMN dim_cert.is_trusted IS '是否可信';
COMMENT ON COLUMN dim_cert.is_parsed_successfully IS '是否解析成功';
COMMENT ON COLUMN dim_cert.is_corrupted IS '是否损坏';
COMMENT ON COLUMN dim_cert.cert_occurrence_count IS '证书出现次数';
COMMENT ON COLUMN dim_cert.threat_score IS '威胁评分';
COMMENT ON COLUMN dim_cert.trust_score IS '信任评分';
COMMENT ON COLUMN dim_cert.threat_level IS '威胁等级';
COMMENT ON COLUMN dim_cert.labels IS '标签列表';
COMMENT ON COLUMN dim_cert.associated_domains IS '关联域名';
COMMENT ON COLUMN dim_cert.associated_ips IS '关联IP';
COMMENT ON COLUMN dim_cert.organization IS '组织信息';
COMMENT ON COLUMN dim_cert.first_seen IS '首次发现时间';
COMMENT ON COLUMN dim_cert.last_seen IS '最后发现时间';
COMMENT ON COLUMN dim_cert.create_time IS '创建时间';
COMMENT ON COLUMN dim_cert.update_time IS '更新时间';
COMMENT ON COLUMN dim_cert.remark IS '备注信息';

-- 内部证书白名单表
DROP TABLE IF EXISTS internal_certificate_whitelist CASCADE;

CREATE TABLE internal_certificate_whitelist (
    id SERIAL PRIMARY KEY,
    cert_sha1 VARCHAR(64) NOT NULL,
    task_id INTEGER NOT NULL,
    link_ip TEXT NOT NULL,
    remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE internal_certificate_whitelist IS '内部证书白名单表';
COMMENT ON COLUMN internal_certificate_whitelist.cert_sha1 IS '证书SHA1哈希值';
COMMENT ON COLUMN internal_certificate_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN internal_certificate_whitelist.link_ip IS '关联IP地址';
COMMENT ON COLUMN internal_certificate_whitelist.remark IS '备注信息';
COMMENT ON COLUMN internal_certificate_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN internal_certificate_whitelist.updated_at IS '更新时间';
COMMENT ON COLUMN internal_certificate_whitelist.created_by IS '创建者用户ID';
COMMENT ON COLUMN internal_certificate_whitelist.updated_by IS '更新者用户ID';

-- 证书标签模型映射表
DROP TABLE IF EXISTS cert_label_model_mapping CASCADE;

CREATE TABLE cert_label_model_mapping (
    id SERIAL PRIMARY KEY,
    label_id INTEGER NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    model_version VARCHAR(50),
    confidence_threshold DOUBLE PRECISION DEFAULT 0.5,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE cert_label_model_mapping IS '证书标签模型映射表';
COMMENT ON COLUMN cert_label_model_mapping.label_id IS '标签ID';
COMMENT ON COLUMN cert_label_model_mapping.model_name IS '模型名称';
COMMENT ON COLUMN cert_label_model_mapping.model_version IS '模型版本';
COMMENT ON COLUMN cert_label_model_mapping.confidence_threshold IS '置信度阈值';
COMMENT ON COLUMN cert_label_model_mapping.enabled IS '是否启用';
COMMENT ON COLUMN cert_label_model_mapping.created_at IS '创建时间';
COMMENT ON COLUMN cert_label_model_mapping.updated_at IS '更新时间';
COMMENT ON COLUMN cert_label_model_mapping.created_by IS '创建者用户ID';
COMMENT ON COLUMN cert_label_model_mapping.updated_by IS '更新者用户ID';

-- 证书统计表
DROP TABLE IF EXISTS certificate_statistics CASCADE;

CREATE TABLE certificate_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    total_certificates INTEGER DEFAULT 0,
    new_certificates INTEGER DEFAULT 0,
    expired_certificates INTEGER DEFAULT 0,
    expiring_soon_certificates INTEGER DEFAULT 0,
    trusted_certificates INTEGER DEFAULT 0,
    untrusted_certificates INTEGER DEFAULT 0,
    high_threat_certificates INTEGER DEFAULT 0,
    medium_threat_certificates INTEGER DEFAULT 0,
    low_threat_certificates INTEGER DEFAULT 0,
    self_signed_certificates INTEGER DEFAULT 0,
    ca_certificates INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE certificate_statistics IS '证书统计表';
COMMENT ON COLUMN certificate_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN certificate_statistics.total_certificates IS '证书总数';
COMMENT ON COLUMN certificate_statistics.new_certificates IS '新增证书数';
COMMENT ON COLUMN certificate_statistics.expired_certificates IS '已过期证书数';
COMMENT ON COLUMN certificate_statistics.expiring_soon_certificates IS '即将过期证书数';
COMMENT ON COLUMN certificate_statistics.trusted_certificates IS '可信证书数';
COMMENT ON COLUMN certificate_statistics.untrusted_certificates IS '不可信证书数';
COMMENT ON COLUMN certificate_statistics.high_threat_certificates IS '高威胁证书数';
COMMENT ON COLUMN certificate_statistics.medium_threat_certificates IS '中威胁证书数';
COMMENT ON COLUMN certificate_statistics.low_threat_certificates IS '低威胁证书数';
COMMENT ON COLUMN certificate_statistics.self_signed_certificates IS '自签名证书数';
COMMENT ON COLUMN certificate_statistics.ca_certificates IS 'CA证书数';
COMMENT ON COLUMN certificate_statistics.created_at IS '创建时间';
COMMENT ON COLUMN certificate_statistics.updated_at IS '更新时间';

-- ========================================
-- 创建索引
-- ========================================

-- cert表索引
CREATE INDEX idx_cert_first_seen ON cert (first_seen);
CREATE INDEX idx_cert_last_seen ON cert (last_seen);
CREATE INDEX idx_cert_threat_level ON cert (threat_level);
CREATE INDEX idx_cert_trust_level ON cert (trust_level);
CREATE INDEX idx_cert_created_at ON cert (created_at);

-- dim_cert表索引
CREATE INDEX idx_dim_cert_der_sha1 ON dim_cert (der_sha1);
CREATE INDEX idx_dim_cert_der_sha256 ON dim_cert (der_sha256);
CREATE INDEX idx_dim_cert_pem_sha1 ON dim_cert (pem_sha1);
CREATE INDEX idx_dim_cert_pem_sha256 ON dim_cert (pem_sha256);
CREATE INDEX idx_dim_cert_issuer_id ON dim_cert (issuer_id);
CREATE INDEX idx_dim_cert_subject_id ON dim_cert (subject_id);
CREATE INDEX idx_dim_cert_not_before ON dim_cert (not_before);
CREATE INDEX idx_dim_cert_not_after ON dim_cert (not_after);
CREATE INDEX idx_dim_cert_common_name ON dim_cert (common_name);
CREATE INDEX idx_dim_cert_threat_level ON dim_cert (threat_level);
CREATE INDEX idx_dim_cert_is_trusted ON dim_cert (is_trusted);
CREATE INDEX idx_dim_cert_first_seen ON dim_cert (first_seen);
CREATE INDEX idx_dim_cert_last_seen ON dim_cert (last_seen);
CREATE INDEX idx_dim_cert_create_time ON dim_cert (create_time);

-- internal_certificate_whitelist表索引
CREATE INDEX idx_internal_cert_whitelist_cert_sha1 ON internal_certificate_whitelist (cert_sha1);
CREATE INDEX idx_internal_cert_whitelist_task_id ON internal_certificate_whitelist (task_id);
CREATE INDEX idx_internal_cert_whitelist_created_at ON internal_certificate_whitelist (created_at);

-- cert_label_model_mapping表索引
CREATE INDEX idx_cert_label_model_label_id ON cert_label_model_mapping (label_id);
CREATE INDEX idx_cert_label_model_model_name ON cert_label_model_mapping (model_name);
CREATE INDEX idx_cert_label_model_enabled ON cert_label_model_mapping (enabled);
CREATE INDEX idx_cert_label_model_created_at ON cert_label_model_mapping (created_at);

-- certificate_statistics表索引
CREATE UNIQUE INDEX idx_certificate_statistics_stat_date ON certificate_statistics (stat_date);
CREATE INDEX idx_certificate_statistics_created_at ON certificate_statistics (created_at);

-- ========================================
-- 创建触发器
-- ========================================

-- cert表更新时间触发器
CREATE OR REPLACE FUNCTION update_cert_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_cert_updated_at
    BEFORE UPDATE ON cert
    FOR EACH ROW
    EXECUTE FUNCTION update_cert_updated_at();

-- dim_cert表更新时间触发器
CREATE OR REPLACE FUNCTION update_dim_cert_update_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_dim_cert_update_time
    BEFORE UPDATE ON dim_cert
    FOR EACH ROW
    EXECUTE FUNCTION update_dim_cert_update_time();

-- internal_certificate_whitelist表更新时间触发器
CREATE OR REPLACE FUNCTION update_internal_certificate_whitelist_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_internal_certificate_whitelist_updated_at
    BEFORE UPDATE ON internal_certificate_whitelist
    FOR EACH ROW
    EXECUTE FUNCTION update_internal_certificate_whitelist_updated_at();

-- cert_label_model_mapping表更新时间触发器
CREATE OR REPLACE FUNCTION update_cert_label_model_mapping_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_cert_label_model_mapping_updated_at
    BEFORE UPDATE ON cert_label_model_mapping
    FOR EACH ROW
    EXECUTE FUNCTION update_cert_label_model_mapping_updated_at();

-- certificate_statistics表更新时间触发器
CREATE OR REPLACE FUNCTION update_certificate_statistics_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_certificate_statistics_updated_at
    BEFORE UPDATE ON certificate_statistics
    FOR EACH ROW
    EXECUTE FUNCTION update_certificate_statistics_updated_at();

-- ========================================
-- 为CDC创建复制标识
-- ========================================

ALTER TABLE cert REPLICA IDENTITY FULL;
ALTER TABLE dim_cert REPLICA IDENTITY FULL;
ALTER TABLE internal_certificate_whitelist REPLICA IDENTITY FULL;
ALTER TABLE cert_label_model_mapping REPLICA IDENTITY FULL;
ALTER TABLE certificate_statistics REPLICA IDENTITY FULL;