-- ========================================
-- NTA 3.0 告警服务数据库结构
-- ========================================
-- 创建时间: 2025-01-23
-- 描述: 告警服务相关表结构定义，包括：
-- - 告警核心业务表（告警主表、告警知识库等）
-- - 告警订阅和通知管理相关表（告警订阅、通知模板、通知发送记录等）
-- - 告警抑制规则表
-- - 告警统计表
-- 数据库: PostgreSQL
--
-- 注意：本文件依赖的枚举类型定义在 01-metadata.sql 中
-- 请确保先执行元数据脚本再执行本脚本
-- ========================================

-- ========================================
-- 告警核心业务表
-- ========================================

-- 告警表（主表）
DROP TABLE IF EXISTS alarms CASCADE;

CREATE TABLE alarms (
    id VARCHAR(32) PRIMARY KEY,
    task_id BIGINT,
    alarm_time TIMESTAMP WITH TIME ZONE,
    alarm_name VARCHAR(255),
    alarm_type VARCHAR(100),
    targets JSONB,                              -- 告警对象（JSON格式存储）
    victims JSONB,                              -- 受害者信息（JSON格式存储）
    attackers JSONB,                            -- 攻击者信息（JSON格式存储）
    attack_level INTEGER DEFAULT 0,
    status INTEGER DEFAULT 0,                   -- 处理状态：0-未处理，1-处理中，2-已处理，3-已关闭
    raw_data JSONB,                            -- 告警原始数据（JSON格式）
    knowledge_id BIGINT,                       -- 关联告警知识库ID
    attack_chain_names TEXT[],                 -- 攻击链名称数组
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE alarms IS '告警表 - 存储所有告警信息';
COMMENT ON COLUMN alarms.id IS '告警唯一标识';
COMMENT ON COLUMN alarms.task_id IS '关联任务ID';
COMMENT ON COLUMN alarms.alarm_time IS '告警发生时间';
COMMENT ON COLUMN alarms.alarm_name IS '告警名称';
COMMENT ON COLUMN alarms.alarm_type IS '告警类型';
COMMENT ON COLUMN alarms.targets IS '告警对象信息（JSON格式）';
COMMENT ON COLUMN alarms.victims IS '受害者信息（JSON格式）';
COMMENT ON COLUMN alarms.attackers IS '攻击者信息（JSON格式）';
COMMENT ON COLUMN alarms.attack_level IS '威胁权重/攻击等级';
COMMENT ON COLUMN alarms.status IS '处理状态：0-未处理，1-处理中，2-已处理，3-已关闭';
COMMENT ON COLUMN alarms.raw_data IS '告警原始数据（JSON格式）';
COMMENT ON COLUMN alarms.knowledge_id IS '关联告警知识库ID';
COMMENT ON COLUMN alarms.attack_chain_names IS '攻击链名称数组';
COMMENT ON COLUMN alarms.created_at IS '创建时间';
COMMENT ON COLUMN alarms.updated_at IS '更新时间';
COMMENT ON COLUMN alarms.created_by IS '创建者用户ID';
COMMENT ON COLUMN alarms.updated_by IS '更新者用户ID';

-- 告警知识库表
DROP TABLE IF EXISTS alarm_knowledge CASCADE;

CREATE TABLE alarm_knowledge (
    id BIGSERIAL PRIMARY KEY,
    knowledge_id BIGINT NOT NULL UNIQUE,
    alarm_name VARCHAR(255) NOT NULL,
    attack_type INTEGER,
    attack_type_name VARCHAR(100),
    relation_tag_ids TEXT,                     -- 关联标签ID，逗号分隔
    exclude_tag_ids TEXT,                      -- 排除标签ID，逗号分隔
    threat_level INTEGER DEFAULT 0,           -- 威胁等级
    description TEXT,
    mitigation_steps TEXT[],                   -- 缓解步骤数组
    references TEXT[],                         -- 参考资料数组
    tags TEXT[],                              -- 标签数组
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE alarm_knowledge IS '告警知识库表 - 存储告警类型的知识和处理方法';
COMMENT ON COLUMN alarm_knowledge.id IS '主键ID';
COMMENT ON COLUMN alarm_knowledge.knowledge_id IS '知识库ID（业务ID）';
COMMENT ON COLUMN alarm_knowledge.alarm_name IS '告警名称';
COMMENT ON COLUMN alarm_knowledge.attack_type IS '攻击类型编号';
COMMENT ON COLUMN alarm_knowledge.attack_type_name IS '攻击类型名称';
COMMENT ON COLUMN alarm_knowledge.relation_tag_ids IS '关联标签ID，逗号分隔';
COMMENT ON COLUMN alarm_knowledge.exclude_tag_ids IS '排除标签ID，逗号分隔';
COMMENT ON COLUMN alarm_knowledge.threat_level IS '威胁等级';
COMMENT ON COLUMN alarm_knowledge.description IS '知识描述';
COMMENT ON COLUMN alarm_knowledge.mitigation_steps IS '缓解步骤';
COMMENT ON COLUMN alarm_knowledge.references IS '参考资料';
COMMENT ON COLUMN alarm_knowledge.tags IS '标签';
COMMENT ON COLUMN alarm_knowledge.is_active IS '是否激活';
COMMENT ON COLUMN alarm_knowledge.created_at IS '创建时间';
COMMENT ON COLUMN alarm_knowledge.updated_at IS '更新时间';
COMMENT ON COLUMN alarm_knowledge.created_by IS '创建者用户ID';
COMMENT ON COLUMN alarm_knowledge.updated_by IS '更新者用户ID';

-- 告警类型配置表（对应AlarmType实体）
DROP TABLE IF EXISTS alarm_type_config CASCADE;

CREATE TABLE alarm_type_config (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(50) UNIQUE NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    default_threat_level VARCHAR(20) DEFAULT 'MEDIUM',
    enabled BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE alarm_type_config IS '告警类型配置表（系统配置用）';
COMMENT ON COLUMN alarm_type_config.type_code IS '告警类型代码，唯一标识';
COMMENT ON COLUMN alarm_type_config.type_name IS '告警类型名称';
COMMENT ON COLUMN alarm_type_config.description IS '告警类型描述';
COMMENT ON COLUMN alarm_type_config.category IS '告警类型分类';
COMMENT ON COLUMN alarm_type_config.default_threat_level IS '默认威胁等级: LOW, MEDIUM, HIGH, CRITICAL';
COMMENT ON COLUMN alarm_type_config.enabled IS '是否启用';
COMMENT ON COLUMN alarm_type_config.sort_order IS '排序序号';

-- ========================================
-- 告警核心表结构补充（基于实体类定义）
-- ========================================

-- 告警主表（对应Alarm聚合根）
DROP TABLE IF EXISTS alarm CASCADE;

CREATE TABLE alarm (
    id VARCHAR(32) PRIMARY KEY,
    alarm_index VARCHAR(100),
    type VARCHAR(50),
    version INTEGER DEFAULT 1,
    score INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 0,
    handler VARCHAR(100),
    handle_time TIMESTAMP,
    handle_note TEXT
);

COMMENT ON TABLE alarm IS '告警主表';
COMMENT ON COLUMN alarm.id IS '告警ID';
COMMENT ON COLUMN alarm.alarm_index IS '告警索引';
COMMENT ON COLUMN alarm.type IS '告警类型';
COMMENT ON COLUMN alarm.version IS '告警版本';
COMMENT ON COLUMN alarm.score IS '告警分数';
COMMENT ON COLUMN alarm.create_time IS '创建时间';
COMMENT ON COLUMN alarm.status IS '处理状态：0-未处理，1-已处理，2-已忽略';
COMMENT ON COLUMN alarm.handler IS '处理人';
COMMENT ON COLUMN alarm.handle_time IS '处理时间';
COMMENT ON COLUMN alarm.handle_note IS '处理备注';

-- 告警源信息表（对应AlarmSource实体）
DROP TABLE IF EXISTS alarm_source CASCADE;

CREATE TABLE alarm_source (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    alarm_status INTEGER DEFAULT 0,
    alarm_type VARCHAR(100),
    attack_chain_name VARCHAR(200),
    attack_family VARCHAR(100),
    ioc TEXT,
    alarm_knowledge_id BIGINT,
    attack_level INTEGER DEFAULT 0,
    alarm_name VARCHAR(200),
    alarm_principle TEXT,
    time BIGINT,
    attack_route TEXT,
    alarm_handle_method VARCHAR(500),
    alarm_related_label TEXT,
    alarm_session_list TEXT,
    attack_chain_list TEXT,
    task_id INTEGER,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alarm_id) REFERENCES alarm (id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_source IS '告警源信息表';
COMMENT ON COLUMN alarm_source.id IS '源信息ID';
COMMENT ON COLUMN alarm_source.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_source.alarm_status IS '告警状态';
COMMENT ON COLUMN alarm_source.alarm_type IS '告警类型';
COMMENT ON COLUMN alarm_source.attack_chain_name IS '攻击链名称';
COMMENT ON COLUMN alarm_source.attack_family IS '攻击家族';
COMMENT ON COLUMN alarm_source.ioc IS 'IOC信息';
COMMENT ON COLUMN alarm_source.alarm_knowledge_id IS '告警知识ID';
COMMENT ON COLUMN alarm_source.attack_level IS '攻击等级';
COMMENT ON COLUMN alarm_source.alarm_name IS '告警名称';
COMMENT ON COLUMN alarm_source.alarm_principle IS '告警原理';
COMMENT ON COLUMN alarm_source.time IS '告警时间戳';
COMMENT ON COLUMN alarm_source.attack_route IS '攻击路径';
COMMENT ON COLUMN alarm_source.alarm_handle_method IS '告警处理方法';
COMMENT ON COLUMN alarm_source.alarm_related_label IS '告警关联标签';
COMMENT ON COLUMN alarm_source.alarm_session_list IS '告警会话列表';
COMMENT ON COLUMN alarm_source.attack_chain_list IS '攻击链列表';
COMMENT ON COLUMN alarm_source.task_id IS '任务ID';

-- 告警攻击者表（对应AlarmAttacker实体）
DROP TABLE IF EXISTS alarm_attacker CASCADE;

CREATE TABLE alarm_attacker (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    ip VARCHAR(45),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alarm_id) REFERENCES alarm (id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_attacker IS '告警攻击者表';
COMMENT ON COLUMN alarm_attacker.id IS '攻击者ID';
COMMENT ON COLUMN alarm_attacker.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_attacker.ip IS '攻击者IP地址';
COMMENT ON COLUMN alarm_attacker.create_time IS '创建时间';

-- 告警受害者表（对应AlarmVictim实体）
DROP TABLE IF EXISTS alarm_victim CASCADE;

CREATE TABLE alarm_victim (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    ip VARCHAR(45),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alarm_id) REFERENCES alarm (id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_victim IS '告警受害者表';
COMMENT ON COLUMN alarm_victim.id IS '受害者ID';
COMMENT ON COLUMN alarm_victim.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_victim.ip IS '受害者IP地址';
COMMENT ON COLUMN alarm_victim.create_time IS '创建时间';

-- 告警目标表（对应AlarmTargets实体）
DROP TABLE IF EXISTS alarm_targets CASCADE;

CREATE TABLE alarm_targets (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    name VARCHAR(255),
    type VARCHAR(45),
    labels TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alarm_id) REFERENCES alarm (id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_targets IS '告警目标表';
COMMENT ON COLUMN alarm_targets.id IS '目标ID';
COMMENT ON COLUMN alarm_targets.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_targets.name IS '目标名称';
COMMENT ON COLUMN alarm_targets.type IS '目标类型';
COMMENT ON COLUMN alarm_targets.labels IS '标签信息';
COMMENT ON COLUMN alarm_targets.create_time IS '创建时间';

-- 告警原因表（对应AlarmReason实体）
DROP TABLE IF EXISTS alarm_reason CASCADE;

CREATE TABLE alarm_reason (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    key VARCHAR(100),
    actual_value TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alarm_id) REFERENCES alarm (id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_reason IS '告警原因表';
COMMENT ON COLUMN alarm_reason.id IS '原因ID';
COMMENT ON COLUMN alarm_reason.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_reason.key IS '原因键';
COMMENT ON COLUMN alarm_reason.actual_value IS '实际值';
COMMENT ON COLUMN alarm_reason.create_time IS '创建时间';

-- ========================================
-- 告警订阅和通知表（属于告警管理功能）
-- ========================================

-- 告警订阅表
DROP TABLE IF EXISTS alarm_subscriptions CASCADE;

CREATE TABLE alarm_subscriptions (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL,
    subscription_name VARCHAR(255) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    priority_level INTEGER DEFAULT 50,
    match_rules JSONB,                         -- 匹配规则（JSON格式存储）
    notification_channels JSONB,              -- 通知渠道配置（JSON格式存储）
    frequency_type VARCHAR(50),               -- 通知频率类型
    frequency_config JSONB,                   -- 频率配置
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_config JSONB,                -- 静默时间配置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE alarm_subscriptions IS '告警订阅表 - 用户告警订阅规则配置';
COMMENT ON COLUMN alarm_subscriptions.id IS '订阅ID';
COMMENT ON COLUMN alarm_subscriptions.user_id IS '用户ID';
COMMENT ON COLUMN alarm_subscriptions.subscription_name IS '订阅名称';
COMMENT ON COLUMN alarm_subscriptions.description IS '订阅描述';
COMMENT ON COLUMN alarm_subscriptions.enabled IS '是否启用';
COMMENT ON COLUMN alarm_subscriptions.priority_level IS '优先级';
COMMENT ON COLUMN alarm_subscriptions.match_rules IS '匹配规则（JSON格式）';
COMMENT ON COLUMN alarm_subscriptions.notification_channels IS '通知渠道配置（JSON格式），包含告警输出配置信息';
COMMENT ON COLUMN alarm_subscriptions.frequency_type IS '通知频率类型';
COMMENT ON COLUMN alarm_subscriptions.frequency_config IS '频率配置';
COMMENT ON COLUMN alarm_subscriptions.quiet_hours_enabled IS '是否启用静默时间';
COMMENT ON COLUMN alarm_subscriptions.quiet_hours_config IS '静默时间配置';
COMMENT ON COLUMN alarm_subscriptions.created_at IS '创建时间';
COMMENT ON COLUMN alarm_subscriptions.updated_at IS '更新时间';
COMMENT ON COLUMN alarm_subscriptions.created_by IS '创建者用户ID';
COMMENT ON COLUMN alarm_subscriptions.updated_by IS '更新者用户ID';

-- 通知模板表
DROP TABLE IF EXISTS notification_templates CASCADE;

CREATE TABLE notification_templates (
    id VARCHAR(32) PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_type VARCHAR(50) NOT NULL,       -- EMAIL, SMS, WEBHOOK, KAFKA
    is_default BOOLEAN DEFAULT FALSE,
    subject_template TEXT,
    content_template TEXT NOT NULL,
    template_variables JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE notification_templates IS '通知模板表 - 存储各种通知渠道的消息模板';
COMMENT ON COLUMN notification_templates.id IS '模板ID';
COMMENT ON COLUMN notification_templates.template_name IS '模板名称';
COMMENT ON COLUMN notification_templates.template_type IS '模板类型：EMAIL, SMS, WEBHOOK, KAFKA';
COMMENT ON COLUMN notification_templates.is_default IS '是否默认模板';
COMMENT ON COLUMN notification_templates.subject_template IS '主题模板';
COMMENT ON COLUMN notification_templates.content_template IS '内容模板';
COMMENT ON COLUMN notification_templates.template_variables IS '模板变量定义（JSON格式）';
COMMENT ON COLUMN notification_templates.enabled IS '是否启用';
COMMENT ON COLUMN notification_templates.created_at IS '创建时间';
COMMENT ON COLUMN notification_templates.updated_at IS '更新时间';
COMMENT ON COLUMN notification_templates.created_by IS '创建者用户ID';
COMMENT ON COLUMN notification_templates.updated_by IS '更新者用户ID';

-- 通知结果表
DROP TABLE IF EXISTS notification_results CASCADE;

CREATE TABLE notification_results (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32),
    subscription_id VARCHAR(32),
    template_id VARCHAR(32),
    channel VARCHAR(50) NOT NULL,             -- EMAIL, SMS, WEBHOOK, KAFKA等
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(500),
    content TEXT,
    status VARCHAR(20) NOT NULL,              -- SUCCESS, FAILED, PENDING
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarms(id) ON DELETE SET NULL,
    FOREIGN KEY (subscription_id) REFERENCES alarm_subscriptions(id) ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE SET NULL
);

COMMENT ON TABLE notification_results IS '通知结果表 - 记录告警通知的发送结果和状态';
COMMENT ON COLUMN notification_results.id IS '结果ID';
COMMENT ON COLUMN notification_results.alarm_id IS '告警ID';
COMMENT ON COLUMN notification_results.subscription_id IS '订阅ID';
COMMENT ON COLUMN notification_results.template_id IS '模板ID';
COMMENT ON COLUMN notification_results.channel IS '通知渠道：EMAIL, SMS, WEBHOOK, KAFKA等';
COMMENT ON COLUMN notification_results.recipient IS '接收者';
COMMENT ON COLUMN notification_results.subject IS '通知主题';
COMMENT ON COLUMN notification_results.content IS '通知内容';
COMMENT ON COLUMN notification_results.status IS '发送状态：SUCCESS, FAILED, PENDING';
COMMENT ON COLUMN notification_results.error_message IS '错误信息';
COMMENT ON COLUMN notification_results.retry_count IS '重试次数';
COMMENT ON COLUMN notification_results.max_retries IS '最大重试次数';
COMMENT ON COLUMN notification_results.sent_at IS '发送时间';
COMMENT ON COLUMN notification_results.created_at IS '创建时间';

-- ========================================
-- 告警抑制规则表
-- ========================================

-- 告警抑制规则表
DROP TABLE IF EXISTS alarm_suppression CASCADE;

CREATE TABLE alarm_suppression (
    id SERIAL PRIMARY KEY,
    victim VARCHAR(255),                           -- 受害者IP（支持通配符*）
    attacker VARCHAR(255),                         -- 攻击者IP（支持通配符*）
    label VARCHAR(255),                            -- 告警相关标签（支持通配符*）
    description TEXT,                              -- 规则描述
    enabled BOOLEAN DEFAULT TRUE,                  -- 是否启用
    operator VARCHAR(100),                         -- 操作者
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expire_time TIMESTAMP WITH TIME ZONE           -- 过期时间（NULL表示永不过期）
);

COMMENT ON TABLE alarm_suppression IS '告警抑制规则表 - 存储告警抑制规则配置';
COMMENT ON COLUMN alarm_suppression.id IS '抑制规则ID';
COMMENT ON COLUMN alarm_suppression.victim IS '受害者IP模式（支持*通配符）';
COMMENT ON COLUMN alarm_suppression.attacker IS '攻击者IP模式（支持*通配符）';
COMMENT ON COLUMN alarm_suppression.label IS '告警标签模式（支持*通配符）';
COMMENT ON COLUMN alarm_suppression.description IS '规则描述';
COMMENT ON COLUMN alarm_suppression.enabled IS '是否启用';
COMMENT ON COLUMN alarm_suppression.operator IS '操作者';
COMMENT ON COLUMN alarm_suppression.create_time IS '创建时间';
COMMENT ON COLUMN alarm_suppression.update_time IS '更新时间';
COMMENT ON COLUMN alarm_suppression.expire_time IS '过期时间（NULL表示永不过期）';

-- ========================================
-- 告警统计表
-- ========================================

-- 告警统计表
DROP TABLE IF EXISTS alarm_statistics CASCADE;

CREATE TABLE alarm_statistics (
    id BIGSERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    stat_hour INTEGER,                         -- 统计小时（0-23，NULL表示全天）
    alarm_type VARCHAR(100),
    alarm_name VARCHAR(255),
    total_count INTEGER DEFAULT 0,
    processed_count INTEGER DEFAULT 0,
    unprocessed_count INTEGER DEFAULT 0,
    avg_process_time INTEGER,                  -- 平均处理时间（秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE (stat_date, stat_hour, alarm_type, alarm_name)
);

COMMENT ON TABLE alarm_statistics IS '告警统计表 - 按日期、小时、类型统计告警数据';
COMMENT ON COLUMN alarm_statistics.id IS '统计ID';
COMMENT ON COLUMN alarm_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN alarm_statistics.stat_hour IS '统计小时（0-23，NULL表示全天）';
COMMENT ON COLUMN alarm_statistics.alarm_type IS '告警类型';
COMMENT ON COLUMN alarm_statistics.alarm_name IS '告警名称';
COMMENT ON COLUMN alarm_statistics.total_count IS '总告警数';
COMMENT ON COLUMN alarm_statistics.processed_count IS '已处理告警数';
COMMENT ON COLUMN alarm_statistics.unprocessed_count IS '未处理告警数';
COMMENT ON COLUMN alarm_statistics.avg_process_time IS '平均处理时间（秒）';
COMMENT ON COLUMN alarm_statistics.created_at IS '创建时间';
COMMENT ON COLUMN alarm_statistics.updated_at IS '更新时间';

-- ========================================
-- 创建索引
-- ========================================

-- 主告警表索引
CREATE INDEX idx_alarms_task_id ON alarms (task_id);
CREATE INDEX idx_alarms_alarm_time ON alarms (alarm_time);
CREATE INDEX idx_alarms_alarm_name ON alarms (alarm_name);
CREATE INDEX idx_alarms_alarm_type ON alarms (alarm_type);
CREATE INDEX idx_alarms_status ON alarms (status);
CREATE INDEX idx_alarms_knowledge_id ON alarms (knowledge_id);
CREATE INDEX idx_alarms_attack_level ON alarms (attack_level);
CREATE INDEX idx_alarms_created_at ON alarms (created_at);
CREATE INDEX idx_alarms_updated_at ON alarms (updated_at);

-- 告警知识库表索引
CREATE INDEX idx_alarm_knowledge_knowledge_id ON alarm_knowledge (knowledge_id);
CREATE INDEX idx_alarm_knowledge_attack_type ON alarm_knowledge (attack_type);
CREATE INDEX idx_alarm_knowledge_alarm_name ON alarm_knowledge (alarm_name);
CREATE INDEX idx_alarm_knowledge_is_active ON alarm_knowledge (is_active);

-- 告警类型配置表索引
CREATE INDEX idx_alarm_type_config_code ON alarm_type_config (type_code);
CREATE INDEX idx_alarm_type_config_category ON alarm_type_config (category);
CREATE INDEX idx_alarm_type_config_enabled ON alarm_type_config (enabled);

-- 告警核心表索引
CREATE INDEX idx_alarm_index ON alarm (alarm_index);
CREATE INDEX idx_alarm_type ON alarm (type);
CREATE INDEX idx_alarm_status ON alarm (status);
CREATE INDEX idx_alarm_create_time ON alarm (create_time);

CREATE INDEX idx_alarm_source_alarm_id ON alarm_source (alarm_id);
CREATE INDEX idx_alarm_source_alarm_type ON alarm_source (alarm_type);
CREATE INDEX idx_alarm_source_task_id ON alarm_source (task_id);
CREATE INDEX idx_alarm_source_time ON alarm_source (time);
CREATE INDEX idx_alarm_source_knowledge_id ON alarm_source (alarm_knowledge_id);

CREATE INDEX idx_alarm_attacker_alarm_id ON alarm_attacker (alarm_id);
CREATE INDEX idx_alarm_attacker_ip ON alarm_attacker (ip);

CREATE INDEX idx_alarm_victim_alarm_id ON alarm_victim (alarm_id);
CREATE INDEX idx_alarm_victim_ip ON alarm_victim (ip);

CREATE INDEX idx_alarm_targets_alarm_id ON alarm_targets (alarm_id);
CREATE INDEX idx_alarm_targets_type ON alarm_targets (type);

CREATE INDEX idx_alarm_reason_alarm_id ON alarm_reason (alarm_id);
CREATE INDEX idx_alarm_reason_key ON alarm_reason (key);

-- 告警订阅表索引
CREATE INDEX idx_alarm_subscriptions_user_id ON alarm_subscriptions (user_id);
CREATE INDEX idx_alarm_subscriptions_enabled ON alarm_subscriptions (enabled);
CREATE INDEX idx_alarm_subscriptions_priority_level ON alarm_subscriptions (priority_level);

-- 通知模板表索引
CREATE INDEX idx_notification_templates_template_type ON notification_templates (template_type);
CREATE INDEX idx_notification_templates_is_default ON notification_templates (is_default);
CREATE INDEX idx_notification_templates_enabled ON notification_templates (enabled);

-- 通知结果表索引
CREATE INDEX idx_notification_results_alarm_id ON notification_results (alarm_id);
CREATE INDEX idx_notification_results_subscription_id ON notification_results (subscription_id);
CREATE INDEX idx_notification_results_template_id ON notification_results (template_id);
CREATE INDEX idx_notification_results_channel ON notification_results (channel);
CREATE INDEX idx_notification_results_status ON notification_results (status);
CREATE INDEX idx_notification_results_created_at ON notification_results (created_at);
CREATE INDEX idx_notification_results_sent_at ON notification_results (sent_at);

-- 告警抑制表索引
CREATE INDEX idx_alarm_suppression_victim ON alarm_suppression (victim);
CREATE INDEX idx_alarm_suppression_attacker ON alarm_suppression (attacker);
CREATE INDEX idx_alarm_suppression_label ON alarm_suppression (label);
CREATE INDEX idx_alarm_suppression_enabled ON alarm_suppression (enabled);
CREATE INDEX idx_alarm_suppression_expire_time ON alarm_suppression (expire_time);
CREATE INDEX idx_alarm_suppression_operator ON alarm_suppression (operator);
CREATE INDEX idx_alarm_suppression_created_at ON alarm_suppression (create_time);

-- 复合索引用于优化匹配查询
CREATE INDEX idx_alarm_suppression_match ON alarm_suppression (victim, attacker, label, enabled);
CREATE INDEX idx_alarm_suppression_active ON alarm_suppression (enabled, expire_time);

-- 告警统计表索引
CREATE INDEX idx_alarm_statistics_stat_date ON alarm_statistics (stat_date);
CREATE INDEX idx_alarm_statistics_alarm_type ON alarm_statistics (alarm_type);
CREATE INDEX idx_alarm_statistics_alarm_name ON alarm_statistics (alarm_name);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为告警相关表创建更新时间触发器
CREATE TRIGGER update_alarms_updated_at
    BEFORE UPDATE ON alarms
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_knowledge_updated_at
    BEFORE UPDATE ON alarm_knowledge
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_type_config_updated_at
    BEFORE UPDATE ON alarm_type_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_subscriptions_updated_at
    BEFORE UPDATE ON alarm_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_templates_updated_at
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_statistics_updated_at
    BEFORE UPDATE ON alarm_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_suppression_updated_at
    BEFORE UPDATE ON alarm_suppression
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 插入默认通知模板数据
-- ========================================

-- 插入默认通知模板
INSERT INTO notification_templates (id, template_name, template_type, is_default, subject_template, content_template, template_variables) VALUES
('default_email_template', '默认邮件模板', 'EMAIL', TRUE,
 '【NTA安全告警】${alarmType} - ${alarmName}',
 '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>安全告警通知</title>
</head>
<body>
    <h2>安全告警通知</h2>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr><td><strong>告警类型</strong></td><td>${alarmType}</td></tr>
        <tr><td><strong>告警名称</strong></td><td>${alarmName}</td></tr>
        <tr><td><strong>告警时间</strong></td><td>${alarmTime}</td></tr>
        <tr><td><strong>攻击等级</strong></td><td>${attackLevel}</td></tr>
        <tr><td><strong>处理状态</strong></td><td>${status}</td></tr>
        <tr><td><strong>攻击链</strong></td><td>${attackChainNames}</td></tr>
    </table>
    <p>请及时处理该安全告警。</p>
</body>
</html>',
 '{"alarmType": "告警类型", "alarmName": "告警名称", "alarmTime": "告警时间", "attackLevel": "攻击等级", "status": "处理状态", "attackChainNames": "攻击链名称"}'::jsonb),

('default_kafka_template', '默认Kafka模板', 'KAFKA', TRUE,
 '${alarmType}告警',
 '{
    "alarmId": "${alarmId}",
    "alarmType": "${alarmType}",
    "alarmName": "${alarmName}",
    "alarmTime": "${alarmTime}",
    "attackLevel": ${attackLevel},
    "status": ${status},
    "attackChainNames": ${attackChainNames},
    "notificationTime": "${notificationTime}"
}',
 '{"alarmId": "告警ID", "alarmType": "告警类型", "alarmName": "告警名称", "alarmTime": "告警时间", "attackLevel": "攻击等级", "status": "处理状态", "attackChainNames": "攻击链名称", "notificationTime": "通知时间"}'::jsonb);

-- ========================================
-- CDC配置
-- ========================================

-- 为所有告警相关表配置CDC
ALTER TABLE alarms REPLICA IDENTITY FULL;
ALTER TABLE alarm_knowledge REPLICA IDENTITY FULL;
ALTER TABLE alarm_type_config REPLICA IDENTITY FULL;
ALTER TABLE alarm REPLICA IDENTITY FULL;
ALTER TABLE alarm_source REPLICA IDENTITY FULL;
ALTER TABLE alarm_attacker REPLICA IDENTITY FULL;
ALTER TABLE alarm_victim REPLICA IDENTITY FULL;
ALTER TABLE alarm_targets REPLICA IDENTITY FULL;
ALTER TABLE alarm_reason REPLICA IDENTITY FULL;
ALTER TABLE alarm_subscriptions REPLICA IDENTITY FULL;
ALTER TABLE notification_templates REPLICA IDENTITY FULL;
ALTER TABLE notification_results REPLICA IDENTITY FULL;
ALTER TABLE alarm_suppression REPLICA IDENTITY FULL;
ALTER TABLE alarm_statistics REPLICA IDENTITY FULL;