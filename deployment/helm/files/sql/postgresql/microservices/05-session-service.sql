-- ========================================
-- Session Service Database Schema
-- 会话服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE session_service;
-- \c session_service;

-- ========================================
-- 核心会话表
-- ========================================

-- 会话表
DROP TABLE IF EXISTS session CASCADE;
CREATE TABLE session (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    src_ip INET NOT NULL,
    dst_ip INET NOT NULL,
    src_port INTEGER NOT NULL,
    dst_port INTEGER NOT NULL,
    protocol VARCHAR(10) NOT NULL,
    session_type VARCHAR(50),
    session_status VARCHAR(20) DEFAULT 'ACTIVE',
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    bytes_sent BIGINT DEFAULT 0,
    bytes_received BIGINT DEFAULT 0,
    packets_sent INTEGER DEFAULT 0,
    packets_received INTEGER DEFAULT 0,
    flags VARCHAR(50),
    tcp_state VARCHAR(20),
    application_protocol VARCHAR(50),
    service_name VARCHAR(100),
    country_code VARCHAR(10),
    region VARCHAR(100),
    city VARCHAR(100),
    organization VARCHAR(200),
    threat_level INTEGER DEFAULT 0,
    risk_score DECIMAL(5,2) DEFAULT 0.0,
    tags JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session IS '会话表';
COMMENT ON COLUMN session.id IS '会话记录ID';
COMMENT ON COLUMN session.session_id IS '会话唯一标识';
COMMENT ON COLUMN session.src_ip IS '源IP地址';
COMMENT ON COLUMN session.dst_ip IS '目标IP地址';
COMMENT ON COLUMN session.src_port IS '源端口';
COMMENT ON COLUMN session.dst_port IS '目标端口';
COMMENT ON COLUMN session.protocol IS '协议类型：TCP, UDP, ICMP';
COMMENT ON COLUMN session.session_type IS '会话类型：INBOUND, OUTBOUND, INTERNAL';
COMMENT ON COLUMN session.session_status IS '会话状态：ACTIVE, CLOSED, TIMEOUT, RESET';
COMMENT ON COLUMN session.start_time IS '会话开始时间';
COMMENT ON COLUMN session.end_time IS '会话结束时间';
COMMENT ON COLUMN session.duration_seconds IS '会话持续时间（秒）';
COMMENT ON COLUMN session.bytes_sent IS '发送字节数';
COMMENT ON COLUMN session.bytes_received IS '接收字节数';
COMMENT ON COLUMN session.packets_sent IS '发送包数';
COMMENT ON COLUMN session.packets_received IS '接收包数';
COMMENT ON COLUMN session.flags IS 'TCP标志位';
COMMENT ON COLUMN session.tcp_state IS 'TCP连接状态';
COMMENT ON COLUMN session.application_protocol IS '应用层协议';
COMMENT ON COLUMN session.service_name IS '服务名称';
COMMENT ON COLUMN session.country_code IS '国家代码';
COMMENT ON COLUMN session.region IS '地区';
COMMENT ON COLUMN session.city IS '城市';
COMMENT ON COLUMN session.organization IS '组织机构';
COMMENT ON COLUMN session.threat_level IS '威胁等级：0-无威胁，1-低，2-中，3-高，4-严重';
COMMENT ON COLUMN session.risk_score IS '风险评分';
COMMENT ON COLUMN session.tags IS '标签';
COMMENT ON COLUMN session.metadata IS '元数据';
COMMENT ON COLUMN session.created_at IS '创建时间';
COMMENT ON COLUMN session.updated_at IS '更新时间';

-- 会话分析表
DROP TABLE IF EXISTS session_analysis CASCADE;
CREATE TABLE session_analysis (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    analysis_result JSONB,
    confidence_score DECIMAL(5,2) DEFAULT 0.0,
    risk_indicators JSONB,
    behavioral_patterns JSONB,
    anomaly_score DECIMAL(5,2) DEFAULT 0.0,
    analysis_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analyzer_version VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE session_analysis IS '会话分析表';
COMMENT ON COLUMN session_analysis.id IS '分析记录ID';
COMMENT ON COLUMN session_analysis.session_id IS '会话ID';
COMMENT ON COLUMN session_analysis.analysis_type IS '分析类型：PROTOCOL, BEHAVIOR, TRAFFIC_PATTERN, SECURITY';
COMMENT ON COLUMN session_analysis.analysis_result IS '分析结果';
COMMENT ON COLUMN session_analysis.confidence_score IS '置信度评分';
COMMENT ON COLUMN session_analysis.risk_indicators IS '风险指标';
COMMENT ON COLUMN session_analysis.behavioral_patterns IS '行为模式';
COMMENT ON COLUMN session_analysis.anomaly_score IS '异常评分';
COMMENT ON COLUMN session_analysis.analysis_timestamp IS '分析时间';
COMMENT ON COLUMN session_analysis.analyzer_version IS '分析器版本';
COMMENT ON COLUMN session_analysis.created_at IS '创建时间';
COMMENT ON COLUMN session_analysis.updated_at IS '更新时间';

-- 会话统计表
DROP TABLE IF EXISTS session_statistics CASCADE;
CREATE TABLE session_statistics (
    id SERIAL PRIMARY KEY,
    stat_time TIMESTAMP NOT NULL,
    stat_interval INTEGER NOT NULL,
    stat_type VARCHAR(50) NOT NULL,
    dimension_key VARCHAR(100),
    dimension_value VARCHAR(200),
    session_count INTEGER DEFAULT 0,
    total_bytes BIGINT DEFAULT 0,
    total_packets INTEGER DEFAULT 0,
    avg_duration DECIMAL(10,2),
    max_duration INTEGER,
    min_duration INTEGER,
    unique_src_ips INTEGER DEFAULT 0,
    unique_dst_ips INTEGER DEFAULT 0,
    top_protocols JSONB,
    top_applications JSONB,
    top_src_ips JSONB,
    top_dst_ips JSONB,
    threat_sessions INTEGER DEFAULT 0,
    anomaly_sessions INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_statistics IS '会话统计表';
COMMENT ON COLUMN session_statistics.id IS '统计记录ID';
COMMENT ON COLUMN session_statistics.stat_time IS '统计时间';
COMMENT ON COLUMN session_statistics.stat_interval IS '统计间隔（秒）';
COMMENT ON COLUMN session_statistics.stat_type IS '统计类型：HOURLY, DAILY, PROTOCOL, APPLICATION, IP';
COMMENT ON COLUMN session_statistics.dimension_key IS '维度键';
COMMENT ON COLUMN session_statistics.dimension_value IS '维度值';
COMMENT ON COLUMN session_statistics.session_count IS '会话数量';
COMMENT ON COLUMN session_statistics.total_bytes IS '总字节数';
COMMENT ON COLUMN session_statistics.total_packets IS '总包数';
COMMENT ON COLUMN session_statistics.avg_duration IS '平均持续时间';
COMMENT ON COLUMN session_statistics.max_duration IS '最大持续时间';
COMMENT ON COLUMN session_statistics.min_duration IS '最小持续时间';
COMMENT ON COLUMN session_statistics.unique_src_ips IS '唯一源IP数量';
COMMENT ON COLUMN session_statistics.unique_dst_ips IS '唯一目标IP数量';
COMMENT ON COLUMN session_statistics.top_protocols IS 'Top协议列表';
COMMENT ON COLUMN session_statistics.top_applications IS 'Top应用列表';
COMMENT ON COLUMN session_statistics.top_src_ips IS 'Top源IP列表';
COMMENT ON COLUMN session_statistics.top_dst_ips IS 'Top目标IP列表';
COMMENT ON COLUMN session_statistics.threat_sessions IS '威胁会话数量';
COMMENT ON COLUMN session_statistics.anomaly_sessions IS '异常会话数量';
COMMENT ON COLUMN session_statistics.created_at IS '创建时间';
COMMENT ON COLUMN session_statistics.updated_at IS '更新时间';

-- 会话标签表
DROP TABLE IF EXISTS session_labels CASCADE;
CREATE TABLE session_labels (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    label_name VARCHAR(100) NOT NULL,
    label_value VARCHAR(200),
    label_type VARCHAR(50) NOT NULL,
    confidence DECIMAL(5,2) DEFAULT 1.0,
    source VARCHAR(50) NOT NULL,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE session_labels IS '会话标签表';
COMMENT ON COLUMN session_labels.id IS '标签记录ID';
COMMENT ON COLUMN session_labels.session_id IS '会话ID';
COMMENT ON COLUMN session_labels.label_name IS '标签名称';
COMMENT ON COLUMN session_labels.label_value IS '标签值';
COMMENT ON COLUMN session_labels.label_type IS '标签类型：MANUAL, AUTO, SYSTEM';
COMMENT ON COLUMN session_labels.confidence IS '置信度';
COMMENT ON COLUMN session_labels.source IS '标签来源：USER, ML_MODEL, RULE_ENGINE';
COMMENT ON COLUMN session_labels.created_by IS '创建者';
COMMENT ON COLUMN session_labels.created_at IS '创建时间';
COMMENT ON COLUMN session_labels.updated_at IS '更新时间';

-- 会话查询历史表
DROP TABLE IF EXISTS session_query_history CASCADE;
CREATE TABLE session_query_history (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    query_name VARCHAR(200),
    query_sql TEXT NOT NULL,
    query_params JSONB,
    result_count INTEGER,
    execution_time_ms INTEGER,
    query_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_query_history IS '会话查询历史表';
COMMENT ON COLUMN session_query_history.id IS '查询记录ID';
COMMENT ON COLUMN session_query_history.user_id IS '用户ID';
COMMENT ON COLUMN session_query_history.query_name IS '查询名称';
COMMENT ON COLUMN session_query_history.query_sql IS '查询SQL';
COMMENT ON COLUMN session_query_history.query_params IS '查询参数';
COMMENT ON COLUMN session_query_history.result_count IS '结果数量';
COMMENT ON COLUMN session_query_history.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN session_query_history.query_timestamp IS '查询时间';
COMMENT ON COLUMN session_query_history.is_favorite IS '是否收藏';
COMMENT ON COLUMN session_query_history.created_at IS '创建时间';

-- 会话查询模板表
DROP TABLE IF EXISTS session_query_template CASCADE;
CREATE TABLE session_query_template (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(200) NOT NULL,
    template_description TEXT,
    template_sql TEXT NOT NULL,
    template_params JSONB,
    category VARCHAR(100),
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_query_template IS '会话查询模板表';
COMMENT ON COLUMN session_query_template.id IS '模板ID';
COMMENT ON COLUMN session_query_template.template_name IS '模板名称';
COMMENT ON COLUMN session_query_template.template_description IS '模板描述';
COMMENT ON COLUMN session_query_template.template_sql IS '模板SQL';
COMMENT ON COLUMN session_query_template.template_params IS '模板参数';
COMMENT ON COLUMN session_query_template.category IS '分类';
COMMENT ON COLUMN session_query_template.is_system IS '是否系统模板';
COMMENT ON COLUMN session_query_template.is_active IS '是否激活';
COMMENT ON COLUMN session_query_template.created_by IS '创建者';
COMMENT ON COLUMN session_query_template.usage_count IS '使用次数';
COMMENT ON COLUMN session_query_template.created_at IS '创建时间';
COMMENT ON COLUMN session_query_template.updated_at IS '更新时间';

-- 会话异常检测表
DROP TABLE IF EXISTS session_anomaly_detection CASCADE;
CREATE TABLE session_anomaly_detection (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    anomaly_type VARCHAR(100) NOT NULL,
    anomaly_score DECIMAL(5,2) NOT NULL,
    threshold_value DECIMAL(10,2),
    actual_value DECIMAL(10,2),
    detection_method VARCHAR(50),
    model_version VARCHAR(50),
    anomaly_details JSONB,
    is_confirmed BOOLEAN DEFAULT FALSE,
    confirmed_by VARCHAR(100),
    confirmed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE session_anomaly_detection IS '会话异常检测表';
COMMENT ON COLUMN session_anomaly_detection.id IS '异常记录ID';
COMMENT ON COLUMN session_anomaly_detection.session_id IS '会话ID';
COMMENT ON COLUMN session_anomaly_detection.anomaly_type IS '异常类型';
COMMENT ON COLUMN session_anomaly_detection.anomaly_score IS '异常评分';
COMMENT ON COLUMN session_anomaly_detection.threshold_value IS '阈值';
COMMENT ON COLUMN session_anomaly_detection.actual_value IS '实际值';
COMMENT ON COLUMN session_anomaly_detection.detection_method IS '检测方法';
COMMENT ON COLUMN session_anomaly_detection.model_version IS '模型版本';
COMMENT ON COLUMN session_anomaly_detection.anomaly_details IS '异常详情';
COMMENT ON COLUMN session_anomaly_detection.is_confirmed IS '是否确认';
COMMENT ON COLUMN session_anomaly_detection.confirmed_by IS '确认者';
COMMENT ON COLUMN session_anomaly_detection.confirmed_at IS '确认时间';
COMMENT ON COLUMN session_anomaly_detection.created_at IS '创建时间';
COMMENT ON COLUMN session_anomaly_detection.updated_at IS '更新时间';

-- 会话聚合表
DROP TABLE IF EXISTS session_aggregation CASCADE;
CREATE TABLE session_aggregation (
    id SERIAL PRIMARY KEY,
    aggregation_time TIMESTAMP NOT NULL,
    aggregation_interval INTEGER NOT NULL,
    aggregation_type VARCHAR(50) NOT NULL,
    group_by_fields JSONB,
    group_key VARCHAR(200),
    session_count INTEGER DEFAULT 0,
    total_bytes BIGINT DEFAULT 0,
    total_packets INTEGER DEFAULT 0,
    avg_duration DECIMAL(10,2),
    max_bytes BIGINT,
    min_bytes BIGINT,
    unique_protocols INTEGER,
    unique_applications INTEGER,
    risk_distribution JSONB,
    threat_distribution JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_aggregation IS '会话聚合表';
COMMENT ON COLUMN session_aggregation.id IS '聚合记录ID';
COMMENT ON COLUMN session_aggregation.aggregation_time IS '聚合时间';
COMMENT ON COLUMN session_aggregation.aggregation_interval IS '聚合间隔（秒）';
COMMENT ON COLUMN session_aggregation.aggregation_type IS '聚合类型';
COMMENT ON COLUMN session_aggregation.group_by_fields IS '分组字段';
COMMENT ON COLUMN session_aggregation.group_key IS '分组键';
COMMENT ON COLUMN session_aggregation.session_count IS '会话数量';
COMMENT ON COLUMN session_aggregation.total_bytes IS '总字节数';
COMMENT ON COLUMN session_aggregation.total_packets IS '总包数';
COMMENT ON COLUMN session_aggregation.avg_duration IS '平均持续时间';
COMMENT ON COLUMN session_aggregation.max_bytes IS '最大字节数';
COMMENT ON COLUMN session_aggregation.min_bytes IS '最小字节数';
COMMENT ON COLUMN session_aggregation.unique_protocols IS '唯一协议数';
COMMENT ON COLUMN session_aggregation.unique_applications IS '唯一应用数';
COMMENT ON COLUMN session_aggregation.risk_distribution IS '风险分布';
COMMENT ON COLUMN session_aggregation.threat_distribution IS '威胁分布';
COMMENT ON COLUMN session_aggregation.created_at IS '创建时间';

-- 会话白名单表
DROP TABLE IF EXISTS session_whitelist CASCADE;
CREATE TABLE session_whitelist (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    src_ip_pattern VARCHAR(100),
    dst_ip_pattern VARCHAR(100),
    src_port_pattern VARCHAR(50),
    dst_port_pattern VARCHAR(50),
    protocol_pattern VARCHAR(20),
    application_pattern VARCHAR(100),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_whitelist IS '会话白名单表';
COMMENT ON COLUMN session_whitelist.id IS '白名单规则ID';
COMMENT ON COLUMN session_whitelist.rule_name IS '规则名称';
COMMENT ON COLUMN session_whitelist.rule_type IS '规则类型';
COMMENT ON COLUMN session_whitelist.src_ip_pattern IS '源IP模式';
COMMENT ON COLUMN session_whitelist.dst_ip_pattern IS '目标IP模式';
COMMENT ON COLUMN session_whitelist.src_port_pattern IS '源端口模式';
COMMENT ON COLUMN session_whitelist.dst_port_pattern IS '目标端口模式';
COMMENT ON COLUMN session_whitelist.protocol_pattern IS '协议模式';
COMMENT ON COLUMN session_whitelist.application_pattern IS '应用模式';
COMMENT ON COLUMN session_whitelist.description IS '描述';
COMMENT ON COLUMN session_whitelist.is_active IS '是否激活';
COMMENT ON COLUMN session_whitelist.created_by IS '创建者';
COMMENT ON COLUMN session_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN session_whitelist.updated_at IS '更新时间';

-- 会话黑名单表
DROP TABLE IF EXISTS session_blacklist CASCADE;
CREATE TABLE session_blacklist (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    src_ip_pattern VARCHAR(100),
    dst_ip_pattern VARCHAR(100),
    src_port_pattern VARCHAR(50),
    dst_port_pattern VARCHAR(50),
    protocol_pattern VARCHAR(20),
    application_pattern VARCHAR(100),
    threat_level INTEGER DEFAULT 1,
    action VARCHAR(50) DEFAULT 'BLOCK',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_blacklist IS '会话黑名单表';
COMMENT ON COLUMN session_blacklist.id IS '黑名单规则ID';
COMMENT ON COLUMN session_blacklist.rule_name IS '规则名称';
COMMENT ON COLUMN session_blacklist.rule_type IS '规则类型';
COMMENT ON COLUMN session_blacklist.src_ip_pattern IS '源IP模式';
COMMENT ON COLUMN session_blacklist.dst_ip_pattern IS '目标IP模式';
COMMENT ON COLUMN session_blacklist.src_port_pattern IS '源端口模式';
COMMENT ON COLUMN session_blacklist.dst_port_pattern IS '目标端口模式';
COMMENT ON COLUMN session_blacklist.protocol_pattern IS '协议模式';
COMMENT ON COLUMN session_blacklist.application_pattern IS '应用模式';
COMMENT ON COLUMN session_blacklist.threat_level IS '威胁等级';
COMMENT ON COLUMN session_blacklist.action IS '处理动作';
COMMENT ON COLUMN session_blacklist.description IS '描述';
COMMENT ON COLUMN session_blacklist.is_active IS '是否激活';
COMMENT ON COLUMN session_blacklist.created_by IS '创建者';
COMMENT ON COLUMN session_blacklist.created_at IS '创建时间';
COMMENT ON COLUMN session_blacklist.updated_at IS '更新时间';

-- 应用识别表
DROP TABLE IF EXISTS application_identification CASCADE;
CREATE TABLE application_identification (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    application_name VARCHAR(100),
    application_category VARCHAR(50),
    application_vendor VARCHAR(100),
    application_version VARCHAR(50),
    confidence_score DECIMAL(5,2) DEFAULT 0.0,
    identification_method VARCHAR(50),
    signature_id VARCHAR(100),
    payload_analysis JSONB,
    behavioral_analysis JSONB,
    risk_assessment JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE application_identification IS '应用识别表';
COMMENT ON COLUMN application_identification.id IS '识别记录ID';
COMMENT ON COLUMN application_identification.session_id IS '会话ID';
COMMENT ON COLUMN application_identification.application_name IS '应用名称';
COMMENT ON COLUMN application_identification.application_category IS '应用分类';
COMMENT ON COLUMN application_identification.application_vendor IS '应用厂商';
COMMENT ON COLUMN application_identification.application_version IS '应用版本';
COMMENT ON COLUMN application_identification.confidence_score IS '置信度评分';
COMMENT ON COLUMN application_identification.identification_method IS '识别方法：DPI, BEHAVIOR, SIGNATURE, ML';
COMMENT ON COLUMN application_identification.signature_id IS '特征签名ID';
COMMENT ON COLUMN application_identification.payload_analysis IS '载荷分析结果';
COMMENT ON COLUMN application_identification.behavioral_analysis IS '行为分析结果';
COMMENT ON COLUMN application_identification.risk_assessment IS '风险评估结果';
COMMENT ON COLUMN application_identification.created_at IS '创建时间';
COMMENT ON COLUMN application_identification.updated_at IS '更新时间';

-- ========================================
-- PCAP文件管理表
-- ========================================

-- 会话PCAP文件表
DROP TABLE IF EXISTS session_pcap_files CASCADE;
CREATE TABLE session_pcap_files (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(200) NOT NULL,
    file_size BIGINT,
    file_hash VARCHAR(64),
    packet_count INTEGER,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    capture_interface VARCHAR(50),
    file_status VARCHAR(20) DEFAULT 'AVAILABLE',
    compression_type VARCHAR(20),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE session_pcap_files IS '会话PCAP文件表';
COMMENT ON COLUMN session_pcap_files.id IS 'PCAP文件记录ID';
COMMENT ON COLUMN session_pcap_files.session_id IS '会话ID';
COMMENT ON COLUMN session_pcap_files.file_path IS '文件路径';
COMMENT ON COLUMN session_pcap_files.file_name IS '文件名';
COMMENT ON COLUMN session_pcap_files.file_size IS '文件大小（字节）';
COMMENT ON COLUMN session_pcap_files.file_hash IS '文件哈希值';
COMMENT ON COLUMN session_pcap_files.packet_count IS '包数量';
COMMENT ON COLUMN session_pcap_files.start_time IS '捕获开始时间';
COMMENT ON COLUMN session_pcap_files.end_time IS '捕获结束时间';
COMMENT ON COLUMN session_pcap_files.capture_interface IS '捕获接口';
COMMENT ON COLUMN session_pcap_files.file_status IS '文件状态：AVAILABLE, ARCHIVED, DELETED';
COMMENT ON COLUMN session_pcap_files.compression_type IS '压缩类型';
COMMENT ON COLUMN session_pcap_files.metadata IS '元数据';
COMMENT ON COLUMN session_pcap_files.created_at IS '创建时间';
COMMENT ON COLUMN session_pcap_files.updated_at IS '更新时间';

-- ========================================
-- 会话备注和关联表
-- ========================================

-- 会话备注表
DROP TABLE IF EXISTS session_remarks CASCADE;
CREATE TABLE session_remarks (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    remark_content TEXT NOT NULL,
    remark_type VARCHAR(50) DEFAULT 'GENERAL',
    priority INTEGER DEFAULT 1,
    is_important BOOLEAN DEFAULT FALSE,
    tags JSONB,
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE session_remarks IS '会话备注表';
COMMENT ON COLUMN session_remarks.id IS '备注记录ID';
COMMENT ON COLUMN session_remarks.session_id IS '会话ID';
COMMENT ON COLUMN session_remarks.remark_content IS '备注内容';
COMMENT ON COLUMN session_remarks.remark_type IS '备注类型：GENERAL, SECURITY, ANALYSIS, INVESTIGATION';
COMMENT ON COLUMN session_remarks.priority IS '优先级：1-低，2-中，3-高';
COMMENT ON COLUMN session_remarks.is_important IS '是否重要';
COMMENT ON COLUMN session_remarks.tags IS '标签';
COMMENT ON COLUMN session_remarks.created_by IS '创建者';
COMMENT ON COLUMN session_remarks.created_at IS '创建时间';
COMMENT ON COLUMN session_remarks.updated_at IS '更新时间';

-- 会话关联表
DROP TABLE IF EXISTS session_relations CASCADE;
CREATE TABLE session_relations (
    id SERIAL PRIMARY KEY,
    source_session_id VARCHAR(100) NOT NULL,
    target_session_id VARCHAR(100) NOT NULL,
    relation_type VARCHAR(50) NOT NULL,
    relation_strength DECIMAL(5,2) DEFAULT 1.0,
    relation_details JSONB,
    discovered_by VARCHAR(50),
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_confirmed BOOLEAN DEFAULT FALSE,
    confirmed_by VARCHAR(100),
    confirmed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_session_id) REFERENCES session(session_id) ON DELETE CASCADE,
    FOREIGN KEY (target_session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE session_relations IS '会话关联表';
COMMENT ON COLUMN session_relations.id IS '关联记录ID';
COMMENT ON COLUMN session_relations.source_session_id IS '源会话ID';
COMMENT ON COLUMN session_relations.target_session_id IS '目标会话ID';
COMMENT ON COLUMN session_relations.relation_type IS '关联类型：SAME_SOURCE_IP, SAME_DESTINATION_IP, SAME_PORT, SAME_APPLICATION, TIME_PROXIMITY';
COMMENT ON COLUMN session_relations.relation_strength IS '关联强度';
COMMENT ON COLUMN session_relations.relation_details IS '关联详情';
COMMENT ON COLUMN session_relations.discovered_by IS '发现方式：AUTO, MANUAL';
COMMENT ON COLUMN session_relations.discovered_at IS '发现时间';
COMMENT ON COLUMN session_relations.is_confirmed IS '是否确认';
COMMENT ON COLUMN session_relations.confirmed_by IS '确认者';
COMMENT ON COLUMN session_relations.confirmed_at IS '确认时间';
COMMENT ON COLUMN session_relations.created_at IS '创建时间';

-- ========================================
-- 索引创建
-- ========================================

-- 会话表索引
CREATE INDEX idx_session_session_id ON session(session_id);
CREATE INDEX idx_session_src_ip ON session(src_ip);
CREATE INDEX idx_session_dst_ip ON session(dst_ip);
CREATE INDEX idx_session_protocol ON session(protocol);
CREATE INDEX idx_session_status ON session(session_status);
CREATE INDEX idx_session_start_time ON session(start_time);
CREATE INDEX idx_session_end_time ON session(end_time);
CREATE INDEX idx_session_threat_level ON session(threat_level);
CREATE INDEX idx_session_risk_score ON session(risk_score);
CREATE INDEX idx_session_application_protocol ON session(application_protocol);
CREATE INDEX idx_session_src_dst_ip ON session(src_ip, dst_ip);
CREATE INDEX idx_session_time_range ON session(start_time, end_time);
CREATE INDEX idx_session_tags ON session USING GIN(tags);

-- 会话分析表索引
CREATE INDEX idx_session_analysis_session_id ON session_analysis(session_id);
CREATE INDEX idx_session_analysis_type ON session_analysis(analysis_type);
CREATE INDEX idx_session_analysis_timestamp ON session_analysis(analysis_timestamp);
CREATE INDEX idx_session_analysis_confidence ON session_analysis(confidence_score);
CREATE INDEX idx_session_analysis_anomaly_score ON session_analysis(anomaly_score);

-- 会话统计表索引
CREATE INDEX idx_session_statistics_stat_time ON session_statistics(stat_time);
CREATE INDEX idx_session_statistics_stat_type ON session_statistics(stat_type);
CREATE INDEX idx_session_statistics_dimension ON session_statistics(dimension_key, dimension_value);
CREATE INDEX idx_session_statistics_interval ON session_statistics(stat_interval);

-- 会话标签表索引
CREATE INDEX idx_session_labels_session_id ON session_labels(session_id);
CREATE INDEX idx_session_labels_name ON session_labels(label_name);
CREATE INDEX idx_session_labels_type ON session_labels(label_type);
CREATE INDEX idx_session_labels_source ON session_labels(source);

-- 会话查询历史表索引
CREATE INDEX idx_session_query_history_user_id ON session_query_history(user_id);
CREATE INDEX idx_session_query_history_timestamp ON session_query_history(query_timestamp);
CREATE INDEX idx_session_query_history_favorite ON session_query_history(is_favorite);

-- 会话查询模板表索引
CREATE INDEX idx_session_query_template_name ON session_query_template(template_name);
CREATE INDEX idx_session_query_template_category ON session_query_template(category);
CREATE INDEX idx_session_query_template_system ON session_query_template(is_system);
CREATE INDEX idx_session_query_template_active ON session_query_template(is_active);

-- 会话异常检测表索引
CREATE INDEX idx_session_anomaly_session_id ON session_anomaly_detection(session_id);
CREATE INDEX idx_session_anomaly_type ON session_anomaly_detection(anomaly_type);
CREATE INDEX idx_session_anomaly_score ON session_anomaly_detection(anomaly_score);
CREATE INDEX idx_session_anomaly_confirmed ON session_anomaly_detection(is_confirmed);

-- 会话聚合表索引
CREATE INDEX idx_session_aggregation_time ON session_aggregation(aggregation_time);
CREATE INDEX idx_session_aggregation_type ON session_aggregation(aggregation_type);
CREATE INDEX idx_session_aggregation_key ON session_aggregation(group_key);
CREATE INDEX idx_session_aggregation_interval ON session_aggregation(aggregation_interval);

-- 会话白名单表索引
CREATE INDEX idx_session_whitelist_name ON session_whitelist(rule_name);
CREATE INDEX idx_session_whitelist_type ON session_whitelist(rule_type);
CREATE INDEX idx_session_whitelist_active ON session_whitelist(is_active);

-- 会话黑名单表索引
CREATE INDEX idx_session_blacklist_name ON session_blacklist(rule_name);
CREATE INDEX idx_session_blacklist_type ON session_blacklist(rule_type);
CREATE INDEX idx_session_blacklist_active ON session_blacklist(is_active);
CREATE INDEX idx_session_blacklist_threat_level ON session_blacklist(threat_level);

-- 应用识别表索引
CREATE INDEX idx_application_identification_session_id ON application_identification(session_id);
CREATE INDEX idx_application_identification_app_name ON application_identification(application_name);
CREATE INDEX idx_application_identification_category ON application_identification(application_category);
CREATE INDEX idx_application_identification_confidence ON application_identification(confidence_score);
CREATE INDEX idx_application_identification_method ON application_identification(identification_method);

-- PCAP文件表索引
CREATE INDEX idx_session_pcap_files_session_id ON session_pcap_files(session_id);
CREATE INDEX idx_session_pcap_files_status ON session_pcap_files(file_status);
CREATE INDEX idx_session_pcap_files_name ON session_pcap_files(file_name);
CREATE INDEX idx_session_pcap_files_hash ON session_pcap_files(file_hash);

-- 会话备注表索引
CREATE INDEX idx_session_remarks_session_id ON session_remarks(session_id);
CREATE INDEX idx_session_remarks_type ON session_remarks(remark_type);
CREATE INDEX idx_session_remarks_important ON session_remarks(is_important);
CREATE INDEX idx_session_remarks_created_by ON session_remarks(created_by);

-- 会话关联表索引
CREATE INDEX idx_session_relations_source ON session_relations(source_session_id);
CREATE INDEX idx_session_relations_target ON session_relations(target_session_id);
CREATE INDEX idx_session_relations_type ON session_relations(relation_type);
CREATE INDEX idx_session_relations_strength ON session_relations(relation_strength);
CREATE INDEX idx_session_relations_confirmed ON session_relations(is_confirmed);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_session_updated_at BEFORE UPDATE ON session
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_analysis_updated_at BEFORE UPDATE ON session_analysis
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_statistics_updated_at BEFORE UPDATE ON session_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_labels_updated_at BEFORE UPDATE ON session_labels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_query_template_updated_at BEFORE UPDATE ON session_query_template
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_anomaly_detection_updated_at BEFORE UPDATE ON session_anomaly_detection
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_whitelist_updated_at BEFORE UPDATE ON session_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_blacklist_updated_at BEFORE UPDATE ON session_blacklist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_application_identification_updated_at BEFORE UPDATE ON application_identification
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_pcap_files_updated_at BEFORE UPDATE ON session_pcap_files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_remarks_updated_at BEFORE UPDATE ON session_remarks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入默认查询模板
INSERT INTO session_query_template (template_name, template_description, template_sql, category, is_system, is_active) VALUES
('查询高风险会话', '查询风险评分大于8的会话', 'SELECT * FROM session WHERE risk_score > 8 ORDER BY risk_score DESC', '安全分析', TRUE, TRUE),
('查询长时间会话', '查询持续时间超过1小时的会话', 'SELECT * FROM session WHERE duration_seconds > 3600 ORDER BY duration_seconds DESC', '性能分析', TRUE, TRUE),
('查询大流量会话', '查询传输字节数超过100MB的会话', 'SELECT * FROM session WHERE (bytes_sent + bytes_received) > 104857600 ORDER BY (bytes_sent + bytes_received) DESC', '流量分析', TRUE, TRUE),
('查询异常端口会话', '查询使用非标准端口的会话', 'SELECT * FROM session WHERE dst_port NOT IN (80, 443, 22, 21, 25, 53, 110, 143, 993, 995) ORDER BY start_time DESC', '异常检测', TRUE, TRUE),
('查询外部通信会话', '查询与外部IP通信的会话', 'SELECT * FROM session WHERE NOT (dst_ip <<= ''10.0.0.0/8'' OR dst_ip <<= ''**********/12'' OR dst_ip <<= ''***********/16'') ORDER BY start_time DESC', '网络分析', TRUE, TRUE);

-- 插入默认白名单规则
INSERT INTO session_whitelist (rule_name, rule_type, dst_ip_pattern, dst_port_pattern, protocol_pattern, description, is_active, created_by) VALUES
('内网DNS查询', 'SYSTEM', '***********', '53', 'UDP', '允许内网DNS查询', TRUE, 'system'),
('内网HTTP访问', 'SYSTEM', '***********/16', '80,8080', 'TCP', '允许内网HTTP访问', TRUE, 'system'),
('内网HTTPS访问', 'SYSTEM', '***********/16', '443,8443', 'TCP', '允许内网HTTPS访问', TRUE, 'system'),
('内网SSH访问', 'SYSTEM', '***********/16', '22', 'TCP', '允许内网SSH访问', TRUE, 'system');

-- 插入默认黑名单规则
INSERT INTO session_blacklist (rule_name, rule_type, dst_port_pattern, protocol_pattern, threat_level, action, description, is_active, created_by) VALUES
('阻止Telnet', 'SECURITY', '23', 'TCP', 2, 'BLOCK', '阻止不安全的Telnet协议', TRUE, 'system'),
('阻止FTP', 'SECURITY', '21', 'TCP', 2, 'BLOCK', '阻止不安全的FTP协议', TRUE, 'system'),
('阻止TFTP', 'SECURITY', '69', 'UDP', 2, 'BLOCK', '阻止不安全的TFTP协议', TRUE, 'system'),
('阻止SNMP', 'SECURITY', '161,162', 'UDP', 1, 'ALERT', '监控SNMP访问', TRUE, 'system');

-- ========================================
-- 分区表创建（针对大数据量表）
-- ========================================

-- 为会话表创建按时间分区（示例）
-- CREATE TABLE session_y2024m01 PARTITION OF session
--     FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 为会话统计表创建按时间分区（示例）
-- CREATE TABLE session_statistics_y2024m01 PARTITION OF session_statistics
--     FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE session REPLICA IDENTITY FULL;
ALTER TABLE session_analysis REPLICA IDENTITY FULL;
ALTER TABLE session_statistics REPLICA IDENTITY FULL;
ALTER TABLE session_labels REPLICA IDENTITY FULL;
ALTER TABLE session_anomaly_detection REPLICA IDENTITY FULL;
ALTER TABLE session_aggregation REPLICA IDENTITY FULL;
ALTER TABLE application_identification REPLICA IDENTITY FULL;
ALTER TABLE session_pcap_files REPLICA IDENTITY FULL;
ALTER TABLE session_remarks REPLICA IDENTITY FULL;
ALTER TABLE session_relations REPLICA IDENTITY FULL;