-- ========================================
-- NTA 3.0 图服务数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 图探索、图谱查询、图可视化等相关的表结构定义
-- 服务: graph-service
-- 数据库: PostgreSQL
-- 说明: 实际的图数据（节点和边）存储在NebulaGraph中，此处仅定义图服务的元数据表
-- ========================================

-- ========================================
-- 图探索功能表
-- ========================================

-- 图探索历史表
DROP TABLE IF EXISTS graph_exploration_history CASCADE;

CREATE TABLE graph_exploration_history (
    id SERIAL PRIMARY KEY,
    atlas_condition TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id INTEGER,
    exploration_name VARCHAR(255),
    exploration_type VARCHAR(50), -- MANUAL, AUTO, SCHEDULED
    result_count INTEGER DEFAULT 0,
    execution_time_ms INTEGER,
    graph_data JSONB,
    filters JSONB,
    layout_config JSONB
);

COMMENT ON TABLE graph_exploration_history IS '图探索历史表';
COMMENT ON COLUMN graph_exploration_history.id IS '探索记录ID';
COMMENT ON COLUMN graph_exploration_history.atlas_condition IS '图谱条件';
COMMENT ON COLUMN graph_exploration_history.created_at IS '创建时间';
COMMENT ON COLUMN graph_exploration_history.updated_at IS '更新时间';
COMMENT ON COLUMN graph_exploration_history.user_id IS '用户ID';
COMMENT ON COLUMN graph_exploration_history.exploration_name IS '探索名称';
COMMENT ON COLUMN graph_exploration_history.exploration_type IS '探索类型';
COMMENT ON COLUMN graph_exploration_history.result_count IS '结果数量';
COMMENT ON COLUMN graph_exploration_history.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN graph_exploration_history.graph_data IS '图数据（JSON格式）';
COMMENT ON COLUMN graph_exploration_history.filters IS '过滤条件（JSON格式）';
COMMENT ON COLUMN graph_exploration_history.layout_config IS '布局配置（JSON格式）';

-- 图探索模板表
DROP TABLE IF EXISTS graph_exploration_template CASCADE;

CREATE TABLE graph_exploration_template (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_description TEXT,
    atlas_condition TEXT NOT NULL,
    filters JSONB,
    layout_config JSONB,
    is_public BOOLEAN DEFAULT FALSE,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER
);

COMMENT ON TABLE graph_exploration_template IS '图探索模板表';
COMMENT ON COLUMN graph_exploration_template.id IS '模板ID';
COMMENT ON COLUMN graph_exploration_template.template_name IS '模板名称';
COMMENT ON COLUMN graph_exploration_template.template_description IS '模板描述';
COMMENT ON COLUMN graph_exploration_template.atlas_condition IS '图谱条件模板';
COMMENT ON COLUMN graph_exploration_template.filters IS '过滤条件模板';
COMMENT ON COLUMN graph_exploration_template.layout_config IS '布局配置模板';
COMMENT ON COLUMN graph_exploration_template.is_public IS '是否公开模板';
COMMENT ON COLUMN graph_exploration_template.created_by IS '创建者用户ID';
COMMENT ON COLUMN graph_exploration_template.updated_by IS '更新者用户ID';

-- ========================================
-- 图分析算法和计算表
-- ========================================

-- 图算法执行记录表
DROP TABLE IF EXISTS graph_algorithm_execution CASCADE;

CREATE TABLE graph_algorithm_execution (
    id BIGSERIAL PRIMARY KEY,
    algorithm_name VARCHAR(100) NOT NULL, -- PAGE_RANK, CENTRALITY, COMMUNITY_DETECTION, SHORTEST_PATH
    execution_name VARCHAR(255),
    input_parameters JSONB NOT NULL,
    execution_status VARCHAR(20) DEFAULT 'RUNNING', -- RUNNING, COMPLETED, FAILED
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    execution_time_ms INTEGER,
    result_data JSONB,
    error_message TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE graph_algorithm_execution IS '图算法执行记录表';
COMMENT ON COLUMN graph_algorithm_execution.id IS '执行记录ID';
COMMENT ON COLUMN graph_algorithm_execution.algorithm_name IS '算法名称';
COMMENT ON COLUMN graph_algorithm_execution.execution_name IS '执行名称';
COMMENT ON COLUMN graph_algorithm_execution.input_parameters IS '输入参数（JSON格式）';
COMMENT ON COLUMN graph_algorithm_execution.execution_status IS '执行状态';
COMMENT ON COLUMN graph_algorithm_execution.start_time IS '开始时间';
COMMENT ON COLUMN graph_algorithm_execution.end_time IS '结束时间';
COMMENT ON COLUMN graph_algorithm_execution.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN graph_algorithm_execution.result_data IS '结果数据（JSON格式）';
COMMENT ON COLUMN graph_algorithm_execution.error_message IS '错误信息';
COMMENT ON COLUMN graph_algorithm_execution.created_by IS '创建者用户ID';

-- 节点重要性评分表
DROP TABLE IF EXISTS node_importance_scores CASCADE;

CREATE TABLE node_importance_scores (
    id BIGSERIAL PRIMARY KEY,
    node_id VARCHAR(255) NOT NULL,
    algorithm_execution_id BIGINT NOT NULL,
    score_type VARCHAR(50) NOT NULL, -- PAGE_RANK, BETWEENNESS, CLOSENESS, DEGREE
    score_value DOUBLE PRECISION NOT NULL,
    rank_position INTEGER,
    percentile DOUBLE PRECISION,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (algorithm_execution_id) REFERENCES graph_algorithm_execution(id)
);

COMMENT ON TABLE node_importance_scores IS '节点重要性评分表';
COMMENT ON COLUMN node_importance_scores.id IS '评分记录ID';
COMMENT ON COLUMN node_importance_scores.node_id IS '节点ID（对应NebulaGraph中的VID）';
COMMENT ON COLUMN node_importance_scores.algorithm_execution_id IS '算法执行记录ID';
COMMENT ON COLUMN node_importance_scores.score_type IS '评分类型';
COMMENT ON COLUMN node_importance_scores.score_value IS '评分值';
COMMENT ON COLUMN node_importance_scores.rank_position IS '排名位置';
COMMENT ON COLUMN node_importance_scores.percentile IS '百分位数';

-- 社区检测结果表
DROP TABLE IF EXISTS community_detection_results CASCADE;

CREATE TABLE community_detection_results (
    id BIGSERIAL PRIMARY KEY,
    algorithm_execution_id BIGINT NOT NULL,
    community_id VARCHAR(100) NOT NULL,
    node_id VARCHAR(255) NOT NULL,
    membership_score DOUBLE PRECISION DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (algorithm_execution_id) REFERENCES graph_algorithm_execution(id)
);

COMMENT ON TABLE community_detection_results IS '社区检测结果表';
COMMENT ON COLUMN community_detection_results.id IS '结果记录ID';
COMMENT ON COLUMN community_detection_results.algorithm_execution_id IS '算法执行记录ID';
COMMENT ON COLUMN community_detection_results.community_id IS '社区ID';
COMMENT ON COLUMN community_detection_results.node_id IS '节点ID（对应NebulaGraph中的VID）';
COMMENT ON COLUMN community_detection_results.membership_score IS '成员归属度';

-- ========================================
-- 图可视化和布局表
-- ========================================

-- 图布局配置表
DROP TABLE IF EXISTS graph_layout_config CASCADE;

CREATE TABLE graph_layout_config (
    id SERIAL PRIMARY KEY,
    layout_name VARCHAR(255) NOT NULL,
    layout_type VARCHAR(50) NOT NULL, -- FORCE_DIRECTED, HIERARCHICAL, CIRCULAR, GRID
    layout_parameters JSONB NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER
);

COMMENT ON TABLE graph_layout_config IS '图布局配置表';
COMMENT ON COLUMN graph_layout_config.id IS '布局配置ID';
COMMENT ON COLUMN graph_layout_config.layout_name IS '布局名称';
COMMENT ON COLUMN graph_layout_config.layout_type IS '布局类型';
COMMENT ON COLUMN graph_layout_config.layout_parameters IS '布局参数（JSON格式）';
COMMENT ON COLUMN graph_layout_config.is_default IS '是否默认布局';
COMMENT ON COLUMN graph_layout_config.created_by IS '创建者用户ID';
COMMENT ON COLUMN graph_layout_config.updated_by IS '更新者用户ID';

-- 图样式配置表
DROP TABLE IF EXISTS graph_style_config CASCADE;

CREATE TABLE graph_style_config (
    id SERIAL PRIMARY KEY,
    style_name VARCHAR(255) NOT NULL,
    node_styles JSONB NOT NULL,
    edge_styles JSONB NOT NULL,
    color_scheme JSONB,
    is_default BOOLEAN DEFAULT FALSE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER
);

COMMENT ON TABLE graph_style_config IS '图样式配置表';
COMMENT ON COLUMN graph_style_config.id IS '样式配置ID';
COMMENT ON COLUMN graph_style_config.style_name IS '样式名称';
COMMENT ON COLUMN graph_style_config.node_styles IS '节点样式（JSON格式）';
COMMENT ON COLUMN graph_style_config.edge_styles IS '边样式（JSON格式）';
COMMENT ON COLUMN graph_style_config.color_scheme IS '颜色方案（JSON格式）';
COMMENT ON COLUMN graph_style_config.is_default IS '是否默认样式';
COMMENT ON COLUMN graph_style_config.created_by IS '创建者用户ID';
COMMENT ON COLUMN graph_style_config.updated_by IS '更新者用户ID';

-- ========================================
-- 创建索引
-- ========================================

-- 图探索历史表索引
CREATE INDEX idx_graph_exploration_history_user_id ON graph_exploration_history (user_id);
CREATE INDEX idx_graph_exploration_history_created_at ON graph_exploration_history (created_at);
CREATE INDEX idx_graph_exploration_history_exploration_type ON graph_exploration_history (exploration_type);

-- 图探索模板表索引
CREATE INDEX idx_graph_exploration_template_created_by ON graph_exploration_template (created_by);
CREATE INDEX idx_graph_exploration_template_is_public ON graph_exploration_template (is_public);
CREATE INDEX idx_graph_exploration_template_template_name ON graph_exploration_template (template_name);

-- 图算法执行记录表索引
CREATE INDEX idx_graph_algorithm_execution_algorithm_name ON graph_algorithm_execution (algorithm_name);
CREATE INDEX idx_graph_algorithm_execution_status ON graph_algorithm_execution (execution_status);
CREATE INDEX idx_graph_algorithm_execution_start_time ON graph_algorithm_execution (start_time);
CREATE INDEX idx_graph_algorithm_execution_created_by ON graph_algorithm_execution (created_by);

-- 节点重要性评分表索引
CREATE INDEX idx_node_importance_scores_node_id ON node_importance_scores (node_id);
CREATE INDEX idx_node_importance_scores_algorithm_execution ON node_importance_scores (algorithm_execution_id);
CREATE INDEX idx_node_importance_scores_score_type ON node_importance_scores (score_type);
CREATE INDEX idx_node_importance_scores_score_value ON node_importance_scores (score_value);

-- 社区检测结果表索引
CREATE INDEX idx_community_detection_algorithm_execution ON community_detection_results (algorithm_execution_id);
CREATE INDEX idx_community_detection_community_id ON community_detection_results (community_id);
CREATE INDEX idx_community_detection_node_id ON community_detection_results (node_id);

-- 图布局配置表索引
CREATE INDEX idx_graph_layout_config_layout_type ON graph_layout_config (layout_type);
CREATE INDEX idx_graph_layout_config_is_default ON graph_layout_config (is_default);
CREATE INDEX idx_graph_layout_config_created_by ON graph_layout_config (created_by);

-- 图样式配置表索引
CREATE INDEX idx_graph_style_config_is_default ON graph_style_config (is_default);
CREATE INDEX idx_graph_style_config_created_by ON graph_style_config (created_by);