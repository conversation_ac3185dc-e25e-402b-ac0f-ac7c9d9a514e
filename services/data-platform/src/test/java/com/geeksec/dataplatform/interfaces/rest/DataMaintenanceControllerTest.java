package com.geeksec.dataplatform.interfaces.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.dataplatform.application.DataMaintenanceApplicationService;
import com.geeksec.dataplatform.interfaces.dto.MaintenanceTaskDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 数据维护控制器测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@WebMvcTest(DataMaintenanceController.class)
@DisplayName("数据维护控制器测试")
class DataMaintenanceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DataMaintenanceApplicationService dataMaintenanceApplicationService;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        // 设置模拟数据
        List<Map<String, Object>> mockPartitions = new ArrayList<>();
        Map<String, Object> partition = new HashMap<>();
        partition.put("partition_name", "p20240101");
        partition.put("partition_value", "2024-01-01");
        partition.put("row_count", 1000L);
        partition.put("data_size_bytes", 1024000L);
        mockPartitions.add(partition);

        when(dataMaintenanceApplicationService.getTablePartitions(anyString()))
                .thenReturn(mockPartitions);
        when(dataMaintenanceApplicationService.getSessionLogPartitions())
                .thenReturn(mockPartitions);
    }

    @Test
    @DisplayName("获取表分区信息")
    void getTablePartitions_whenValidTableName_thenReturnPartitions() throws Exception {
        mockMvc.perform(get("/api/data-platform/maintenance/partitions/dwd_session_logs"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].partition_name").value("p20240101"));
    }

    @Test
    @DisplayName("获取会话日志表分区信息")
    void getSessionLogPartitions_whenCalled_thenReturnPartitions() throws Exception {
        mockMvc.perform(get("/api/data-platform/maintenance/partitions/session-logs"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("检查过期分区")
    void checkExpiredPartitions_whenValidParameters_thenReturnExpiredPartitions() throws Exception {
        List<Map<String, Object>> expiredPartitions = new ArrayList<>();
        when(dataMaintenanceApplicationService.checkExpiredPartitions(anyString(), anyInt()))
                .thenReturn(expiredPartitions);

        mockMvc.perform(get("/api/data-platform/maintenance/partitions/dwd_session_logs/expired")
                        .param("retentionDays", "90"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("获取维护任务列表")
    void getMaintenanceTasks_whenCalled_thenReturnTasks() throws Exception {
        List<Map<String, Object>> tasks = new ArrayList<>();
        Map<String, Object> task = new HashMap<>();
        task.put("task_id", "task-001");
        task.put("task_name", "数据清理任务");
        task.put("task_type", "DATA_CLEANUP");
        task.put("status", "PENDING");
        tasks.add(task);

        when(dataMaintenanceApplicationService.getMaintenanceTasks())
                .thenReturn(tasks);

        mockMvc.perform(get("/api/data-platform/maintenance/tasks"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].task_name").value("数据清理任务"));
    }

    @Test
    @DisplayName("创建维护任务")
    void createMaintenanceTask_whenValidTask_thenReturnSuccess() throws Exception {
        MaintenanceTaskDto taskDto = new MaintenanceTaskDto();
        taskDto.setTaskName("测试维护任务");
        taskDto.setTaskType("DATA_CLEANUP");
        taskDto.setPriority("NORMAL");
        taskDto.setTargetTable("dwd_session_logs");
        taskDto.setScheduledTime(LocalDateTime.now().plusHours(1));

        Map<String, Object> result = new HashMap<>();
        result.put("status", "SUCCESS");
        result.put("message", "维护任务创建成功");
        result.put("task_id", "task-002");

        when(dataMaintenanceApplicationService.createMaintenanceTask(any(Map.class)))
                .thenReturn(result);

        String taskJson = objectMapper.writeValueAsString(taskDto);

        mockMvc.perform(post("/api/data-platform/maintenance/tasks")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(taskJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("SUCCESS"));
    }
}
