package com.geeksec.dataplatform.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 分区信息值对象
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
public class PartitionInfo implements ValueObject {
    
    /**
     * 分区名称
     */
    private String partitionName;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 分区类型
     */
    private PartitionType partitionType;
    
    /**
     * 分区状态
     */
    private PartitionStatus status;
    
    /**
     * 分区大小（字节）
     */
    private Long sizeBytes;
    
    /**
     * 行数
     */
    private Long rowCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifyTime;
    
    /**
     * 分区范围开始值
     */
    private String rangeStart;
    
    /**
     * 分区范围结束值
     */
    private String rangeEnd;
    
    /**
     * 是否为热分区
     */
    private Boolean isHot;
    
    /**
     * 压缩比率
     */
    private Double compressionRatio;
    
    /**
     * 分区类型枚举
     */
    public enum PartitionType {
        RANGE("范围分区"),
        LIST("列表分区"),
        HASH("哈希分区");
        
        private final String description;
        
        PartitionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 分区状态枚举
     */
    public enum PartitionStatus {
        NORMAL("正常"),
        COMPACTING("压缩中"),
        EXPIRED("已过期"),
        DELETING("删除中"),
        ERROR("错误");
        
        private final String description;
        
        PartitionStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 检查分区是否过期
     * 
     * @param retentionDays 保留天数
     * @return 是否过期
     */
    public boolean isExpired(int retentionDays) {
        if (createTime == null) {
            return false;
        }
        return createTime.isBefore(LocalDateTime.now().minusDays(retentionDays));
    }
    
    /**
     * 检查分区是否需要压缩
     * 
     * @param compressionThreshold 压缩阈值
     * @return 是否需要压缩
     */
    public boolean needsCompaction(double compressionThreshold) {
        return compressionRatio != null && compressionRatio < compressionThreshold;
    }
    
    /**
     * 获取分区大小（MB）
     * 
     * @return 分区大小（MB）
     */
    public double getSizeMB() {
        return sizeBytes != null ? sizeBytes / (1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取分区大小（GB）
     * 
     * @return 分区大小（GB）
     */
    public double getSizeGB() {
        return sizeBytes != null ? sizeBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
}
