package com.geeksec.dataplatform;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 数据平台管理服务启动类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class DataPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(DataPlatformApplication.class, args);
    }
}
