package com.geeksec.dataplatform.domain.repository;

import com.geeksec.dataplatform.domain.model.MaintenanceTask;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 维护任务仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface MaintenanceTaskRepository {
    
    /**
     * 根据ID查找任务
     * 
     * @param taskId 任务ID
     * @return 任务信息
     */
    Optional<MaintenanceTask> findById(String taskId);
    
    /**
     * 查找所有任务
     * 
     * @return 任务列表
     */
    List<MaintenanceTask> findAll();
    
    /**
     * 根据状态查找任务
     * 
     * @param status 任务状态
     * @return 任务列表
     */
    List<MaintenanceTask> findByStatus(MaintenanceTask.TaskStatus status);
    
    /**
     * 根据类型查找任务
     * 
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<MaintenanceTask> findByTaskType(MaintenanceTask.TaskType taskType);
    
    /**
     * 根据目标表查找任务
     * 
     * @param targetTable 目标表名
     * @return 任务列表
     */
    List<MaintenanceTask> findByTargetTable(String targetTable);
    
    /**
     * 根据创建者查找任务
     * 
     * @param createdBy 创建者
     * @return 任务列表
     */
    List<MaintenanceTask> findByCreatedBy(String createdBy);
    
    /**
     * 查找计划执行时间在指定范围内的任务
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务列表
     */
    List<MaintenanceTask> findByScheduledTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找待执行的任务
     * 
     * @return 任务列表
     */
    List<MaintenanceTask> findPendingTasks();
    
    /**
     * 查找正在执行的任务
     * 
     * @return 任务列表
     */
    List<MaintenanceTask> findRunningTasks();
    
    /**
     * 查找已完成的任务
     * 
     * @param limit 限制数量
     * @return 任务列表
     */
    List<MaintenanceTask> findCompletedTasks(int limit);
    
    /**
     * 查找失败的任务
     * 
     * @param limit 限制数量
     * @return 任务列表
     */
    List<MaintenanceTask> findFailedTasks(int limit);
    
    /**
     * 保存任务
     * 
     * @param task 任务信息
     * @return 保存后的任务
     */
    MaintenanceTask save(MaintenanceTask task);
    
    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     */
    void deleteById(String taskId);
    
    /**
     * 批量删除已完成的任务
     * 
     * @param beforeTime 指定时间之前的任务
     * @return 删除的任务数量
     */
    int deleteCompletedTasksBefore(LocalDateTime beforeTime);
    
    /**
     * 检查任务是否存在
     * 
     * @param taskId 任务ID
     * @return 是否存在
     */
    boolean existsById(String taskId);
    
    /**
     * 统计任务数量
     * 
     * @param status 任务状态
     * @return 任务数量
     */
    long countByStatus(MaintenanceTask.TaskStatus status);
    
    /**
     * 统计指定时间范围内的任务数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务数量
     */
    long countByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
}
