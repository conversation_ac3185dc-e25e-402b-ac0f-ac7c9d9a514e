package com.geeksec.dataplatform.domain.service;

import com.geeksec.dataplatform.domain.model.PartitionInfo;
import com.geeksec.dataplatform.domain.model.StorageStats;

import java.util.List;

/**
 * 数据访问领域服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DataAccessDomainService {
    
    /**
     * 获取表分区信息
     * 
     * @param tableName 表名
     * @return 分区信息列表
     */
    List<PartitionInfo> getTablePartitions(String tableName);
    
    /**
     * 删除过期分区
     * 
     * @param tableName 表名
     * @param partitionNames 分区名称列表
     * @return 删除成功的分区数量
     */
    int deleteExpiredPartitions(String tableName, List<String> partitionNames);
    
    /**
     * 压缩表数据
     * 
     * @param tableName 表名
     * @return 压缩是否成功
     */
    boolean compactTableData(String tableName);
    
    /**
     * 刷新表统计信息
     * 
     * @param tableName 表名
     * @return 刷新是否成功
     */
    boolean refreshTableStatistics(String tableName);
    
    /**
     * 检查数据完整性
     * 
     * @param tableName 表名
     * @param checkType 检查类型
     * @return 检查是否通过
     */
    boolean checkDataIntegrity(String tableName, String checkType);
    
    /**
     * 获取数据增长趋势
     * 
     * @param tableName 表名
     * @param days 天数
     * @return 存储统计列表
     */
    List<StorageStats> getDataGrowthTrend(String tableName, int days);
    
    /**
     * 获取存储统计信息
     * 
     * @return 存储统计信息
     */
    StorageStats getStorageStats();
}
