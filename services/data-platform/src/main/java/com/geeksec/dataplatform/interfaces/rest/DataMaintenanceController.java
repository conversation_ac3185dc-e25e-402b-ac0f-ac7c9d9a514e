package com.geeksec.dataplatform.interfaces.rest;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.dataplatform.application.DataMaintenanceApplicationService;
import com.geeksec.dataplatform.interfaces.dto.DataLifecycleConfigDto;
import com.geeksec.dataplatform.interfaces.dto.MaintenanceTaskDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据维护控制器 - 基于Doris动态分区的数据生命周期管理
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Tag(name = "数据维护管理", description = "提供数据分区管理、生命周期管理、备份恢复等功能")
@RestController
@RequestMapping("/api/data-platform/maintenance")
@RequiredArgsConstructor
@Validated
public class DataMaintenanceController {

    private final DataMaintenanceApplicationService dataMaintenanceApplicationService;
    
    // ==================== 分区管理接口 ====================
    
    @Operation(summary = "获取表分区信息", description = "获取指定表的所有分区信息")
    @GetMapping("/partitions/{tableName}")
    public ApiResponse<List<Map<String, Object>>> getTablePartitions(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable @NotBlank(message = "表名不能为空") String tableName) {
        log.debug("获取表分区信息, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceApplicationService.getTablePartitions(tableName));
    }
    
    @Operation(summary = "获取会话日志表分区信息", description = "获取会话日志表的分区信息")
    @GetMapping("/partitions/session-logs")
    public ApiResponse<List<Map<String, Object>>> getSessionLogPartitions() {
        log.debug("获取会话日志表分区信息");
        return ApiResponse.success(dataMaintenanceApplicationService.getSessionLogPartitions());
    }
    
    @Operation(summary = "检查过期分区", description = "检查指定表的过期分区")
    @GetMapping("/partitions/{tableName}/expired")
    public ApiResponse<List<Map<String, Object>>> checkExpiredPartitions(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName,
            @Parameter(description = "保留天数", example = "90")
            @RequestParam(defaultValue = "90") int retentionDays) {
        log.debug("检查过期分区, 表名: {}, 保留天数: {}", tableName, retentionDays);
        return ApiResponse.success(dataMaintenanceApplicationService.checkExpiredPartitions(tableName, retentionDays));
    }
    
    @Operation(summary = "删除过期分区", description = "删除指定表的过期分区")
    @DeleteMapping("/partitions/{tableName}/expired")
    public ApiResponse<Map<String, Object>> deleteExpiredPartitions(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName,
            @Parameter(description = "保留天数", example = "90")
            @RequestParam(defaultValue = "90") int retentionDays) {
        log.debug("删除过期分区, 表名: {}, 保留天数: {}", tableName, retentionDays);
        return ApiResponse.success(dataMaintenanceApplicationService.deleteExpiredPartitions(tableName, retentionDays));
    }
    
    // ==================== 存储管理接口 ====================
    
    @Operation(summary = "获取存储统计信息", description = "获取指定表的存储统计信息")
    @GetMapping("/storage/{tableName}/stats")
    public ApiResponse<Map<String, Object>> getStorageStats(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName) {
        log.debug("获取存储统计信息, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceApplicationService.getStorageStats(tableName));
    }
    
    @Operation(summary = "执行数据压缩", description = "对指定表或分区执行数据压缩优化")
    @PostMapping("/storage/compaction")
    public ApiResponse<Map<String, Object>> executeDataCompaction(
            @Parameter(description = "表名", required = true)
            @RequestParam String tableName,
            @Parameter(description = "分区名", example = "p20240101")
            @RequestParam(required = false) String partitionName) {
        log.debug("执行数据压缩, 表名: {}, 分区名: {}", tableName, partitionName);
        return ApiResponse.success(dataMaintenanceApplicationService.executeDataCompaction(tableName, partitionName));
    }
    
    // ==================== 生命周期配置接口 ====================
    
    @Operation(summary = "获取数据生命周期配置", description = "获取指定表的数据生命周期配置")
    @GetMapping("/lifecycle/{tableName}")
    public ApiResponse<Map<String, Object>> getDataLifecycleConfig(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName) {
        log.debug("获取数据生命周期配置, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceApplicationService.getDataLifecycleConfig(tableName));
    }
    
    @Operation(summary = "更新数据生命周期配置", description = "更新指定表的数据生命周期配置")
    @PutMapping("/lifecycle/{tableName}")
    public ApiResponse<Map<String, Object>> updateDataLifecycleConfig(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName,
            @Parameter(description = "生命周期配置", required = true)
            @RequestBody @Valid DataLifecycleConfigDto configDto) {
        log.debug("更新数据生命周期配置, 表名: {}, 配置: {}", tableName, configDto);
        
        // 转换DTO为Map
        Map<String, Object> configMap = convertConfigDtoToMap(configDto);
        
        return ApiResponse.success(dataMaintenanceApplicationService.updateDataLifecycleConfig(tableName, configMap));
    }
    
    // ==================== 维护任务管理接口 ====================
    
    @Operation(summary = "获取维护任务列表", description = "获取所有维护任务列表")
    @GetMapping("/tasks")
    public ApiResponse<List<Map<String, Object>>> getMaintenanceTasks() {
        log.debug("获取维护任务列表");
        return ApiResponse.success(dataMaintenanceApplicationService.getMaintenanceTasks());
    }

    @Operation(summary = "创建维护任务", description = "创建新的维护任务")
    @PostMapping("/tasks")
    public ApiResponse<Map<String, Object>> createMaintenanceTask(
            @Parameter(description = "任务配置", required = true)
            @RequestBody @Valid MaintenanceTaskDto taskDto) {
        log.debug("创建维护任务, 配置: {}", taskDto);

        // 转换DTO为Map
        Map<String, Object> taskConfig = convertTaskDtoToMap(taskDto);

        return ApiResponse.success(dataMaintenanceApplicationService.createMaintenanceTask(taskConfig));
    }
    
    @Operation(summary = "获取维护任务详情", description = "根据任务ID获取维护任务详情")
    @GetMapping("/tasks/{taskId}")
    public ApiResponse<Map<String, Object>> getMaintenanceTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        log.debug("获取维护任务详情, 任务ID: {}", taskId);
        return ApiResponse.success(dataMaintenanceApplicationService.getMaintenanceTask(taskId));
    }
    
    @Operation(summary = "执行维护任务", description = "立即执行指定的维护任务")
    @PostMapping("/tasks/{taskId}/execute")
    public ApiResponse<Map<String, Object>> executeMaintenanceTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        log.debug("执行维护任务, 任务ID: {}", taskId);
        return ApiResponse.success(dataMaintenanceApplicationService.executeMaintenanceTask(taskId));
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 转换配置DTO为Map
     */
    private Map<String, Object> convertConfigDtoToMap(DataLifecycleConfigDto dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("table_name", dto.getTableName());
        map.put("retention_days", dto.getRetentionDays());
        map.put("auto_partition_enabled", dto.getAutoPartitionEnabled());
        map.put("partition_column", dto.getPartitionColumn());
        map.put("partition_granularity", dto.getPartitionGranularity());
        map.put("hot_partition_num", dto.getHotPartitionNum());
        map.put("warm_partition_num", dto.getWarmPartitionNum());
        map.put("cold_partition_num", dto.getColdPartitionNum());
        map.put("auto_cleanup_enabled", dto.getAutoCleanupEnabled());
        map.put("compression_enabled", dto.getCompressionEnabled());
        map.put("compression_delay_hours", dto.getCompressionDelayHours());
        map.put("compression_threshold", dto.getCompressionThreshold());
        return map;
    }
    
    /**
     * 转换任务DTO为Map
     */
    private Map<String, Object> convertTaskDtoToMap(MaintenanceTaskDto dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("task_name", dto.getTaskName());
        map.put("task_type", dto.getTaskType());
        map.put("priority", dto.getPriority() != null ? dto.getPriority() : "NORMAL");
        map.put("target_table", dto.getTargetTable() != null ? dto.getTargetTable() : "");
        map.put("scheduled_time", dto.getScheduledTime() != null ? dto.getScheduledTime() : LocalDateTime.now());
        map.put("task_config", dto.getTaskConfig() != null ? dto.getTaskConfig() : Map.of());
        return map;
    }
}
