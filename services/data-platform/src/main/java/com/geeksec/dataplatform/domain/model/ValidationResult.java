package com.geeksec.dataplatform.domain.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果值对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class ValidationResult implements ValueObject {
    
    /**
     * 错误信息列表
     */
    private List<String> errors = new ArrayList<>();
    
    /**
     * 警告信息列表
     */
    private List<String> warnings = new ArrayList<>();
    
    /**
     * 添加错误信息
     * 
     * @param error 错误信息
     */
    public void addError(String error) {
        errors.add(error);
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        warnings.add(warning);
    }
    
    /**
     * 检查是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return errors.isEmpty();
    }
    
    /**
     * 检查是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * 获取所有错误信息的字符串表示
     * 
     * @return 错误信息字符串
     */
    public String getErrorMessage() {
        return String.join("; ", errors);
    }
    
    /**
     * 获取所有警告信息的字符串表示
     *
     * @return 警告信息字符串
     */
    public String getWarningMessage() {
        return String.join("; ", warnings);
    }

    /**
     * 创建成功的验证结果
     *
     * @return 成功的验证结果
     */
    public static ValidationResult success() {
        return new ValidationResult();
    }
}
