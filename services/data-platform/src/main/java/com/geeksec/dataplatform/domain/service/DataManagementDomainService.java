package com.geeksec.dataplatform.domain.service;

import com.geeksec.dataplatform.domain.model.DataLifecycleConfig;
import com.geeksec.dataplatform.domain.model.MaintenanceTask;
import com.geeksec.dataplatform.domain.model.PartitionInfo;
import com.geeksec.dataplatform.domain.model.StorageStats;
import com.geeksec.dataplatform.domain.model.ValidationResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据管理领域服务接口 - 统一的数据管理领域服务
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DataManagementDomainService {
    
    /**
     * 获取表分区信息
     * 
     * @param tableName 表名
     * @return 分区信息列表
     */
    List<PartitionInfo> getTablePartitions(String tableName);
    
    /**
     * 获取数据生命周期配置
     * 
     * @param tableName 表名
     * @return 生命周期配置
     */
    DataLifecycleConfig getDataLifecycleConfig(String tableName);
    
    /**
     * 创建数据生命周期配置
     * 
     * @param config 配置信息
     * @param createdBy 创建者
     * @return 验证结果
     */
    ValidationResult createDataLifecycleConfig(DataLifecycleConfig config, String createdBy);
    
    /**
     * 更新数据生命周期配置
     * 
     * @param config 配置信息
     * @param updatedBy 更新者
     * @return 验证结果
     */
    ValidationResult updateDataLifecycleConfig(DataLifecycleConfig config, String updatedBy);
    
    /**
     * 删除数据生命周期配置
     * 
     * @param tableName 表名
     * @return 是否成功
     */
    boolean deleteDataLifecycleConfig(String tableName);
    
    /**
     * 检查过期分区
     *
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @return 过期分区列表
     */
    List<PartitionInfo> checkExpiredPartitions(String tableName, int retentionDays);

    /**
     * 删除过期分区
     *
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @param operator 操作者
     * @return 是否成功
     */
    boolean deleteExpiredPartitions(String tableName, int retentionDays, String operator);
    
    /**
     * 清理过期分区
     * 
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @param executor 执行者
     * @return 清理的分区数量
     */
    int cleanupExpiredPartitions(String tableName, int retentionDays, String executor);
    
    /**
     * 获取存储统计信息
     * 
     * @return 存储统计信息
     */
    StorageStats getStorageStats();
    
    /**
     * 获取表存储统计信息
     * 
     * @param tableName 表名
     * @return 存储统计信息
     */
    StorageStats getTableStorageStats(String tableName);
    
    /**
     * 创建维护任务
     * 
     * @param task 任务信息
     * @param createdBy 创建者
     * @return 验证结果
     */
    ValidationResult createMaintenanceTask(MaintenanceTask task, String createdBy);
    
    /**
     * 执行维护任务
     * 
     * @param taskId 任务ID
     * @param executor 执行者
     * @return 是否成功启动
     */
    boolean executeMaintenanceTask(String taskId, String executor);
    
    /**
     * 取消维护任务
     * 
     * @param taskId 任务ID
     * @param executor 执行者
     * @return 是否成功取消
     */
    boolean cancelMaintenanceTask(String taskId, String executor);
    
    /**
     * 获取维护任务
     * 
     * @param taskId 任务ID
     * @return 任务信息
     */
    MaintenanceTask getMaintenanceTask(String taskId);
    
    /**
     * 获取维护任务列表
     * 
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    List<MaintenanceTask> getMaintenanceTasks(MaintenanceTask.TaskStatus status);
    
    /**
     * 执行数据压缩
     * 
     * @param tableName 表名
     * @param partitionName 分区名（可选）
     * @param executor 执行者
     * @return 压缩任务
     */
    MaintenanceTask executeDataCompaction(String tableName, String partitionName, String executor);
    
    /**
     * 执行数据备份
     * 
     * @param tableName 表名
     * @param backupType 备份类型
     * @param executor 执行者
     * @return 备份任务
     */
    MaintenanceTask executeDataBackup(String tableName, String backupType, String executor);
    
    /**
     * 执行数据恢复
     * 
     * @param backupId 备份ID
     * @param targetTable 目标表名
     * @param executor 执行者
     * @return 恢复任务
     */
    MaintenanceTask executeDataRestore(String backupId, String targetTable, String executor);
    
    /**
     * 刷新表统计信息
     * 
     * @param tableName 表名
     * @param executor 执行者
     * @return 刷新任务
     */
    MaintenanceTask refreshTableStatistics(String tableName, String executor);
    
    /**
     * 检查数据完整性
     * 
     * @param tableName 表名
     * @param checkType 检查类型
     * @param executor 执行者
     * @return 检查任务
     */
    MaintenanceTask checkDataIntegrity(String tableName, String checkType, String executor);
    
    /**
     * 获取系统健康状态
     * 
     * @return 健康状态信息
     */
    StorageStats.StorageHealthStatus getSystemHealthStatus();
    
    /**
     * 计算数据增长趋势
     * 
     * @param tableName 表名
     * @param days 统计天数
     * @return 增长趋势数据
     */
    List<StorageStats> calculateDataGrowthTrend(String tableName, int days);
}
