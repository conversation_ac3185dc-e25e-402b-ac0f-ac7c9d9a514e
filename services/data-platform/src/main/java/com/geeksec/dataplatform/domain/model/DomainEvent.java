package com.geeksec.dataplatform.domain.model;

import java.time.LocalDateTime;

/**
 * 领域事件基类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public abstract class DomainEvent {
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredOn;
    
    /**
     * 构造函数
     */
    protected DomainEvent() {
        this.occurredOn = LocalDateTime.now();
    }
    
    /**
     * 获取事件发生时间
     * 
     * @return 事件发生时间
     */
    public LocalDateTime getOccurredOn() {
        return occurredOn;
    }
}
