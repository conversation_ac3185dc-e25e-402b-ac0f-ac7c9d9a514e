package com.geeksec.dataplatform.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 存储统计信息值对象
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
public class StorageStats implements ValueObject {
    
    /**
     * 总存储空间（字节）
     */
    private Long totalStorageBytes;
    
    /**
     * 已使用存储空间（字节）
     */
    private Long usedStorageBytes;
    
    /**
     * 可用存储空间（字节）
     */
    private Long availableStorageBytes;
    
    /**
     * 使用率百分比
     */
    private Double usagePercentage;
    
    /**
     * 会话日志大小（字节）
     */
    private Long sessionLogsSizeBytes;
    
    /**
     * 索引大小（字节）
     */
    private Long indexesSizeBytes;
    
    /**
     * 临时文件大小（字节）
     */
    private Long tempFilesSizeBytes;
    
    /**
     * 备份文件大小（字节）
     */
    private Long backupSizeBytes;
    
    /**
     * 每日增长率（字节/天）
     */
    private Double growthRateBytesPerDay;
    
    /**
     * 预计存储满的日期
     */
    private LocalDateTime estimatedFullDate;
    
    /**
     * 统计时间
     */
    private LocalDateTime statsTime;
    
    /**
     * 获取总存储空间（GB）
     * 
     * @return 总存储空间（GB）
     */
    public double getTotalStorageGB() {
        return totalStorageBytes != null ? totalStorageBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取已使用存储空间（GB）
     * 
     * @return 已使用存储空间（GB）
     */
    public double getUsedStorageGB() {
        return usedStorageBytes != null ? usedStorageBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取可用存储空间（GB）
     * 
     * @return 可用存储空间（GB）
     */
    public double getAvailableStorageGB() {
        return availableStorageBytes != null ? availableStorageBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取会话日志大小（GB）
     * 
     * @return 会话日志大小（GB）
     */
    public double getSessionLogsSizeGB() {
        return sessionLogsSizeBytes != null ? sessionLogsSizeBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取索引大小（GB）
     * 
     * @return 索引大小（GB）
     */
    public double getIndexesSizeGB() {
        return indexesSizeBytes != null ? indexesSizeBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取临时文件大小（GB）
     * 
     * @return 临时文件大小（GB）
     */
    public double getTempFilesSizeGB() {
        return tempFilesSizeBytes != null ? tempFilesSizeBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取备份文件大小（GB）
     * 
     * @return 备份文件大小（GB）
     */
    public double getBackupSizeGB() {
        return backupSizeBytes != null ? backupSizeBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取每日增长率（GB/天）
     * 
     * @return 每日增长率（GB/天）
     */
    public double getGrowthRateGBPerDay() {
        return growthRateBytesPerDay != null ? growthRateBytesPerDay / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 检查存储空间是否告警
     * 
     * @param warningThreshold 告警阈值（百分比）
     * @return 是否告警
     */
    public boolean isStorageWarning(double warningThreshold) {
        return usagePercentage != null && usagePercentage >= warningThreshold;
    }
    
    /**
     * 检查存储空间是否严重告警
     * 
     * @param criticalThreshold 严重告警阈值（百分比）
     * @return 是否严重告警
     */
    public boolean isStorageCritical(double criticalThreshold) {
        return usagePercentage != null && usagePercentage >= criticalThreshold;
    }
    
    /**
     * 计算剩余可用天数
     * 
     * @return 剩余可用天数
     */
    public Integer getRemainingDays() {
        if (availableStorageBytes == null || growthRateBytesPerDay == null || growthRateBytesPerDay <= 0) {
            return null;
        }
        return (int) (availableStorageBytes / growthRateBytesPerDay);
    }
    
    /**
     * 获取存储健康状态
     * 
     * @return 存储健康状态
     */
    public StorageHealthStatus getHealthStatus() {
        if (usagePercentage == null) {
            return StorageHealthStatus.UNKNOWN;
        }
        
        if (usagePercentage < 70) {
            return StorageHealthStatus.HEALTHY;
        } else if (usagePercentage < 85) {
            return StorageHealthStatus.WARNING;
        } else if (usagePercentage < 95) {
            return StorageHealthStatus.CRITICAL;
        } else {
            return StorageHealthStatus.EMERGENCY;
        }
    }
    
    /**
     * 存储健康状态枚举
     */
    public enum StorageHealthStatus {
        HEALTHY("健康"),
        WARNING("警告"),
        CRITICAL("严重"),
        EMERGENCY("紧急"),
        UNKNOWN("未知");
        
        private final String description;
        
        StorageHealthStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
