package com.geeksec.dataplatform.interfaces.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 维护任务DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "维护任务")
public class MaintenanceTaskDto {
    
    @Schema(description = "任务名称", required = true, example = "数据清理任务")
    @NotBlank(message = "任务名称不能为空")
    private String taskName;
    
    @Schema(description = "任务类型", required = true, example = "DATA_CLEANUP", 
            allowableValues = {"DATA_CLEANUP", "DATA_BACKUP", "DATA_RESTORE", "DATA_COMPACTION", "PARTITION_MANAGEMENT", "STATISTICS_REFRESH", "HEALTH_CHECK"})
    @NotBlank(message = "任务类型不能为空")
    private String taskType;
    
    @Schema(description = "任务优先级", example = "NORMAL", 
            allowableValues = {"LOW", "NORMAL", "HIGH", "URGENT"})
    private String priority;
    
    @Schema(description = "目标表名", example = "dwd_session_logs")
    private String targetTable;
    
    @Schema(description = "计划执行时间", example = "2024-01-01T10:00:00")
    private LocalDateTime scheduledTime;
    
    @Schema(description = "任务配置参数")
    private Map<String, Object> taskConfig;
}
