package com.geeksec.dataplatform.domain.repository;

import com.geeksec.dataplatform.domain.model.PartitionInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 分区信息仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface PartitionInfoRepository {
    
    /**
     * 根据表名和分区名查找分区信息
     * 
     * @param tableName 表名
     * @param partitionName 分区名
     * @return 分区信息
     */
    Optional<PartitionInfo> findByTableNameAndPartitionName(String tableName, String partitionName);
    
    /**
     * 根据表名查找所有分区信息
     * 
     * @param tableName 表名
     * @return 分区信息列表
     */
    List<PartitionInfo> findByTableName(String tableName);
    
    /**
     * 根据表名和状态查找分区信息
     * 
     * @param tableName 表名
     * @param status 分区状态
     * @return 分区信息列表
     */
    List<PartitionInfo> findByTableNameAndStatus(String tableName, PartitionInfo.PartitionStatus status);
    
    /**
     * 根据表名和分区类型查找分区信息
     * 
     * @param tableName 表名
     * @param partitionType 分区类型
     * @return 分区信息列表
     */
    List<PartitionInfo> findByTableNameAndPartitionType(String tableName, PartitionInfo.PartitionType partitionType);
    
    /**
     * 查找热分区
     * 
     * @param tableName 表名
     * @return 热分区列表
     */
    List<PartitionInfo> findHotPartitions(String tableName);
    
    /**
     * 查找过期分区
     * 
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @return 过期分区列表
     */
    List<PartitionInfo> findExpiredPartitions(String tableName, int retentionDays);
    
    /**
     * 查找需要压缩的分区
     * 
     * @param tableName 表名
     * @param compressionThreshold 压缩阈值
     * @return 需要压缩的分区列表
     */
    List<PartitionInfo> findPartitionsNeedingCompaction(String tableName, double compressionThreshold);
    
    /**
     * 查找大于指定大小的分区
     * 
     * @param tableName 表名
     * @param sizeThresholdBytes 大小阈值（字节）
     * @return 分区列表
     */
    List<PartitionInfo> findPartitionsLargerThan(String tableName, long sizeThresholdBytes);
    
    /**
     * 查找指定时间范围内创建的分区
     * 
     * @param tableName 表名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分区列表
     */
    List<PartitionInfo> findPartitionsCreatedBetween(String tableName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 保存分区信息
     * 
     * @param partitionInfo 分区信息
     * @return 保存后的分区信息
     */
    PartitionInfo save(PartitionInfo partitionInfo);
    
    /**
     * 批量保存分区信息
     * 
     * @param partitionInfos 分区信息列表
     * @return 保存后的分区信息列表
     */
    List<PartitionInfo> saveAll(List<PartitionInfo> partitionInfos);
    
    /**
     * 删除分区信息
     * 
     * @param tableName 表名
     * @param partitionName 分区名
     */
    void deleteByTableNameAndPartitionName(String tableName, String partitionName);
    
    /**
     * 删除表的所有分区信息
     * 
     * @param tableName 表名
     */
    void deleteByTableName(String tableName);
    
    /**
     * 检查分区是否存在
     * 
     * @param tableName 表名
     * @param partitionName 分区名
     * @return 是否存在
     */
    boolean existsByTableNameAndPartitionName(String tableName, String partitionName);
    
    /**
     * 统计表的分区数量
     * 
     * @param tableName 表名
     * @return 分区数量
     */
    long countByTableName(String tableName);
    
    /**
     * 统计表的指定状态分区数量
     * 
     * @param tableName 表名
     * @param status 分区状态
     * @return 分区数量
     */
    long countByTableNameAndStatus(String tableName, PartitionInfo.PartitionStatus status);
    
    /**
     * 计算表的总存储大小
     * 
     * @param tableName 表名
     * @return 总存储大小（字节）
     */
    long sumSizeByTableName(String tableName);
    
    /**
     * 计算表的总行数
     * 
     * @param tableName 表名
     * @return 总行数
     */
    long sumRowCountByTableName(String tableName);
}
