package com.geeksec.dataplatform.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 数据生命周期配置聚合根
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DataLifecycleConfig extends AggregateRoot<String> {
    
    /**
     * 配置ID
     */
    private String configId;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 数据保留天数
     */
    private Integer retentionDays;
    
    /**
     * 是否启用自动分区
     */
    private Boolean autoPartitionEnabled;
    
    /**
     * 分区列名
     */
    private String partitionColumn;
    
    /**
     * 分区粒度
     */
    private PartitionGranularity partitionGranularity;
    
    /**
     * 热分区数量
     */
    private Integer hotPartitionNum;
    
    /**
     * 温分区数量
     */
    private Integer warmPartitionNum;
    
    /**
     * 冷分区数量
     */
    private Integer coldPartitionNum;
    
    /**
     * 是否启用自动清理
     */
    private Boolean autoCleanupEnabled;
    
    /**
     * 是否启用压缩
     */
    private Boolean compressionEnabled;
    
    /**
     * 压缩延迟小时数
     */
    private Integer compressionDelayHours;
    
    /**
     * 压缩阈值
     */
    private Double compressionThreshold;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新者
     */
    private String updatedBy;
    
    /**
     * 分区粒度枚举
     */
    public enum PartitionGranularity {
        HOUR("小时"),
        DAY("天"),
        WEEK("周"),
        MONTH("月");
        
        private final String description;
        
        PartitionGranularity(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 验证配置有效性
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();
        
        if (tableName == null || tableName.trim().isEmpty()) {
            result.addError("表名不能为空");
        }
        
        if (retentionDays == null || retentionDays <= 0) {
            result.addError("数据保留天数必须大于0");
        }
        
        if (autoPartitionEnabled && (partitionColumn == null || partitionColumn.trim().isEmpty())) {
            result.addError("启用自动分区时，分区列不能为空");
        }
        
        if (hotPartitionNum != null && hotPartitionNum < 0) {
            result.addError("热分区数量不能为负数");
        }
        
        if (warmPartitionNum != null && warmPartitionNum < 0) {
            result.addError("温分区数量不能为负数");
        }
        
        if (coldPartitionNum != null && coldPartitionNum < 0) {
            result.addError("冷分区数量不能为负数");
        }
        
        if (compressionEnabled && compressionDelayHours != null && compressionDelayHours < 0) {
            result.addError("压缩延迟小时数不能为负数");
        }
        
        if (compressionThreshold != null && (compressionThreshold < 0 || compressionThreshold > 1)) {
            result.addError("压缩阈值必须在0-1之间");
        }
        
        return result;
    }
    
    /**
     * 计算总分区数量
     * 
     * @return 总分区数量
     */
    public int getTotalPartitionNum() {
        int total = 0;
        if (hotPartitionNum != null) total += hotPartitionNum;
        if (warmPartitionNum != null) total += warmPartitionNum;
        if (coldPartitionNum != null) total += coldPartitionNum;
        return total;
    }
    
    /**
     * 检查是否需要清理
     * 
     * @return 是否需要清理
     */
    public boolean needsCleanup() {
        return autoCleanupEnabled != null && autoCleanupEnabled;
    }
    
    /**
     * 检查是否需要压缩
     * 
     * @return 是否需要压缩
     */
    public boolean needsCompression() {
        return compressionEnabled != null && compressionEnabled;
    }
    
    /**
     * 更新配置
     *
     * @param updatedBy 更新者
     */
    public void updateConfig(String updatedBy) {
        this.updatedBy = updatedBy;
        this.updateTime = LocalDateTime.now();
    }

    @Override
    public String getId() {
        return this.configId;
    }
}
