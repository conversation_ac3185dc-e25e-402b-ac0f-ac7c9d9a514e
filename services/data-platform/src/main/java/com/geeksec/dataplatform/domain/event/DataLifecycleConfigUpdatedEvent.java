package com.geeksec.dataplatform.domain.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 数据生命周期配置更新事件
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DataLifecycleConfigUpdatedEvent extends DomainEvent {
    
    /**
     * 配置ID
     */
    private String configId;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 旧的保留天数
     */
    private Integer oldRetentionDays;
    
    /**
     * 新的保留天数
     */
    private Integer newRetentionDays;
    
    /**
     * 是否启用自动清理
     */
    private Boolean autoCleanupEnabled;
    
    /**
     * 是否启用压缩
     */
    private Boolean compressionEnabled;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 构造函数
     */
    public DataLifecycleConfigUpdatedEvent() {
        super();
    }
    
    /**
     * 构造函数
     * 
     * @param configId 配置ID
     * @param tableName 表名
     * @param oldRetentionDays 旧的保留天数
     * @param newRetentionDays 新的保留天数
     * @param autoCleanupEnabled 是否启用自动清理
     * @param compressionEnabled 是否启用压缩
     * @param operationType 操作类型
     * @param initiatedBy 事件发起者
     */
    public DataLifecycleConfigUpdatedEvent(String configId, String tableName, 
                                         Integer oldRetentionDays, Integer newRetentionDays,
                                         Boolean autoCleanupEnabled, Boolean compressionEnabled,
                                         String operationType, String initiatedBy) {
        super(configId, "DataLifecycleConfig", initiatedBy);
        this.configId = configId;
        this.tableName = tableName;
        this.oldRetentionDays = oldRetentionDays;
        this.newRetentionDays = newRetentionDays;
        this.autoCleanupEnabled = autoCleanupEnabled;
        this.compressionEnabled = compressionEnabled;
        this.operationType = operationType;
    }
}
