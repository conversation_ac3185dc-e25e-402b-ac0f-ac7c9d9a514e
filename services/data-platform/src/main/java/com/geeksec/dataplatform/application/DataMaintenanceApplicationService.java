package com.geeksec.dataplatform.application;

import com.geeksec.dataplatform.domain.service.DataManagementDomainService;
import com.geeksec.dataplatform.domain.model.DataLifecycleConfig;
import com.geeksec.dataplatform.domain.model.MaintenanceTask;
import com.geeksec.dataplatform.domain.model.PartitionInfo;
import com.geeksec.dataplatform.domain.model.StorageStats;
import com.geeksec.dataplatform.domain.model.ValidationResult;
import com.geeksec.dataplatform.application.converter.DataMaintenanceConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据维护应用服务 - 协调领域服务和基础设施
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataMaintenanceApplicationService {

    private final DataManagementDomainService dataManagementDomainService;
    private final DataMaintenanceConverter converter;
    
    /**
     * 获取Doris表分区信息
     * 
     * @param tableName 表名
     * @return 分区信息列表
     */
    public List<Map<String, Object>> getTablePartitions(String tableName) {
        log.debug("获取表分区信息, 表名: {}", tableName);
        
        List<PartitionInfo> partitions = dataManagementDomainService.getTablePartitions(tableName);
        return partitions.stream()
                .map(converter::partitionInfoToMap)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取会话日志表分区信息
     * 
     * @return 分区信息列表
     */
    public List<Map<String, Object>> getSessionLogPartitions() {
        log.debug("获取会话日志表分区信息");
        return getTablePartitions("dwd_session_logs");
    }
    
    /**
     * 获取分区存储统计信息
     * 
     * @param tableName 表名
     * @return 存储统计信息
     */
    public Map<String, Object> getPartitionStorageStats(String tableName) {
        log.debug("获取分区存储统计信息, 表名: {}", tableName);
        
        StorageStats stats = dataManagementDomainService.getTableStorageStats(tableName);
        return converter.storageStatsToMap(stats);
    }

    /**
     * 获取数据生命周期配置
     *
     * @param tableName 表名
     * @return 生命周期配置
     */
    public Map<String, Object> getDataLifecycleConfig(String tableName) {
        log.debug("获取数据生命周期配置, 表名: {}", tableName);

        DataLifecycleConfig config = dataManagementDomainService.getDataLifecycleConfig(tableName);
        return converter.dataLifecycleConfigToMap(config);
    }
    
    /**
     * 更新数据生命周期配置
     * 
     * @param tableName 表名
     * @param configMap 新的配置
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateDataLifecycleConfig(String tableName, Map<String, Object> configMap) {
        log.debug("更新数据生命周期配置, 表名: {}, 配置: {}", tableName, configMap);
        
        try {
            DataLifecycleConfig config = converter.mapToDataLifecycleConfig(tableName, configMap);
            ValidationResult result = dataManagementDomainService.updateDataLifecycleConfig(config, getCurrentUser());
            
            Map<String, Object> response = new HashMap<>();
            if (result.isValid()) {
                response.put("status", "SUCCESS");
                response.put("message", "数据生命周期配置更新成功");
                response.put("table_name", tableName);
                response.put("update_time", LocalDateTime.now());
            } else {
                response.put("status", "FAILED");
                response.put("message", "配置验证失败: " + result.getErrorMessage());
                response.put("errors", result.getErrors());
            }
            
            return response;
        } catch (Exception e) {
            log.error("更新数据生命周期配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "更新配置时发生错误: " + e.getMessage());
            return response;
        }
    }
    
    /**
     * 检查过期分区
     *
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @return 过期分区列表
     */
    public List<Map<String, Object>> checkExpiredPartitions(String tableName, int retentionDays) {
        log.debug("检查过期分区, 表名: {}, 保留天数: {}", tableName, retentionDays);

        List<PartitionInfo> expiredPartitions = dataManagementDomainService.checkExpiredPartitions(tableName, retentionDays);
        return expiredPartitions.stream()
                .map(converter::partitionInfoToMap)
                .collect(Collectors.toList());
    }

    /**
     * 删除过期分区
     *
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteExpiredPartitions(String tableName, int retentionDays) {
        log.debug("删除过期分区, 表名: {}, 保留天数: {}", tableName, retentionDays);

        try {
            boolean success = dataManagementDomainService.deleteExpiredPartitions(tableName, retentionDays, getCurrentUser());

            Map<String, Object> result = new HashMap<>();
            result.put("result_code", success ? "0" : "1");
            result.put("result_desc", success ? "删除过期分区成功" : "删除过期分区失败");
            result.put("table_name", tableName);
            result.put("retention_days", retentionDays);

            return result;
        } catch (Exception e) {
            log.error("删除过期分区失败, 表名: {}, 保留天数: {}", tableName, retentionDays, e);

            Map<String, Object> result = new HashMap<>();
            result.put("result_code", "1");
            result.put("result_desc", "删除过期分区失败: " + e.getMessage());
            result.put("table_name", tableName);
            result.put("retention_days", retentionDays);

            return result;
        }
    }

    /**
     * 获取存储统计信息
     *
     * @param tableName 表名
     * @return 存储统计信息
     */
    public Map<String, Object> getStorageStats(String tableName) {
        log.debug("获取存储统计信息, 表名: {}", tableName);

        StorageStats stats = dataManagementDomainService.getTableStorageStats(tableName);
        return converter.storageStatsToMap(stats);
    }

    /**
     * 获取数据清理状态
     *
     * @return 清理状态信息
     */
    public Map<String, Object> getDataCleanupStatus() {
        log.debug("获取数据清理状态");

        StorageStats stats = dataManagementDomainService.getStorageStats();
        Map<String, Object> status = new HashMap<>();
        
        status.put("auto_cleanup_enabled", true);
        status.put("last_cleanup_time", LocalDateTime.now().minusHours(2));
        status.put("next_cleanup_time", LocalDateTime.now().plusHours(22));
        status.put("cleanup_interval_hours", 24);
        status.put("storage_usage_percentage", stats.getUsagePercentage());
        status.put("health_status", stats.getHealthStatus().getDescription());
        
        return status;
    }
    
    /**
     * 获取存储空间使用情况
     * 
     * @return 存储使用情况
     */
    public Map<String, Object> getStorageUsage() {
        log.debug("获取存储空间使用情况");
        
        StorageStats stats = dataManagementDomainService.getStorageStats();
        return converter.storageStatsToMap(stats);
    }

    /**
     * 获取数据增长趋势
     *
     * @param days 统计天数
     * @return 数据增长趋势
     */
    public List<Map<String, Object>> getDataGrowthTrend(int days) {
        log.debug("获取数据增长趋势, 统计天数: {}", days);

        List<StorageStats> trendData = dataManagementDomainService.calculateDataGrowthTrend("dwd_session_logs", days);
        return trendData.stream()
                .map(converter::storageStatsToMap)
                .collect(Collectors.toList());
    }
    
    /**
     * 执行数据压缩优化
     * 
     * @param tableName 表名
     * @param partitionName 分区名（可选）
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeDataCompaction(String tableName, String partitionName) {
        log.debug("执行数据压缩优化, 表名: {}, 分区名: {}", tableName, partitionName);
        
        try {
            MaintenanceTask task = dataManagementDomainService.executeDataCompaction(tableName, partitionName, getCurrentUser());
            return converter.maintenanceTaskToMap(task);
        } catch (Exception e) {
            log.error("执行数据压缩失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "执行数据压缩时发生错误: " + e.getMessage());
            return response;
        }
    }
    
    /**
     * 获取维护任务列表
     * 
     * @return 维护任务列表
     */
    public List<Map<String, Object>> getMaintenanceTasks() {
        log.debug("获取维护任务列表");
        
        List<MaintenanceTask> tasks = dataManagementDomainService.getMaintenanceTasks(null);
        return tasks.stream()
                .map(converter::maintenanceTaskToMap)
                .collect(Collectors.toList());
    }
    
    /**
     * 创建维护任务
     * 
     * @param taskConfig 任务配置
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> createMaintenanceTask(Map<String, Object> taskConfig) {
        log.debug("创建维护任务, 配置: {}", taskConfig);
        
        try {
            MaintenanceTask task = converter.mapToMaintenanceTask(taskConfig);
            ValidationResult result = dataManagementDomainService.createMaintenanceTask(task, getCurrentUser());
            
            Map<String, Object> response = new HashMap<>();
            if (result.isValid()) {
                response.put("status", "SUCCESS");
                response.put("message", "维护任务创建成功");
                response.put("task_id", task.getTaskId());
                response.put("create_time", task.getCreateTime());
            } else {
                response.put("status", "FAILED");
                response.put("message", "任务验证失败: " + result.getErrorMessage());
                response.put("errors", result.getErrors());
            }
            
            return response;
        } catch (Exception e) {
            log.error("创建维护任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "创建任务时发生错误: " + e.getMessage());
            return response;
        }
    }
    
    /**
     * 执行维护任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeMaintenanceTask(String taskId) {
        log.debug("执行维护任务, 任务ID: {}", taskId);

        try {
            boolean success = dataManagementDomainService.executeMaintenanceTask(taskId, getCurrentUser());

            Map<String, Object> response = new HashMap<>();
            if (success) {
                response.put("status", "SUCCESS");
                response.put("message", "维护任务开始执行");
                response.put("task_id", taskId);
                response.put("start_time", LocalDateTime.now());
            } else {
                response.put("status", "FAILED");
                response.put("message", "任务执行失败，请检查任务状态");
                response.put("task_id", taskId);
            }

            return response;
        } catch (Exception e) {
            log.error("执行维护任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "执行任务时发生错误: " + e.getMessage());
            return response;
        }
    }

    /**
     * 取消维护任务
     *
     * @param taskId 任务ID
     * @return 取消结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> cancelMaintenanceTask(String taskId) {
        log.debug("取消维护任务, 任务ID: {}", taskId);

        try {
            boolean success = dataManagementDomainService.cancelMaintenanceTask(taskId, getCurrentUser());

            Map<String, Object> response = new HashMap<>();
            if (success) {
                response.put("status", "SUCCESS");
                response.put("message", "维护任务已取消");
                response.put("task_id", taskId);
                response.put("cancel_time", LocalDateTime.now());
            } else {
                response.put("status", "FAILED");
                response.put("message", "任务取消失败，请检查任务状态");
                response.put("task_id", taskId);
            }

            return response;
        } catch (Exception e) {
            log.error("取消维护任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "取消任务时发生错误: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取维护任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    public Map<String, Object> getMaintenanceTask(String taskId) {
        log.debug("获取维护任务详情, 任务ID: {}", taskId);

        MaintenanceTask task = dataManagementDomainService.getMaintenanceTask(taskId);
        return converter.maintenanceTaskToMap(task);
    }

    /**
     * 获取当前用户
     *
     * @return 当前用户名
     */
    private String getCurrentUser() {
        // TODO: 从安全上下文获取当前用户
        return "system";
    }
}
