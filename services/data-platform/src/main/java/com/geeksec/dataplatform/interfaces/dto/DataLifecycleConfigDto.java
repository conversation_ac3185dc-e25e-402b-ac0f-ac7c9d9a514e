package com.geeksec.dataplatform.interfaces.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 数据生命周期配置DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "数据生命周期配置")
public class DataLifecycleConfigDto {
    
    @Schema(description = "表名", required = true, example = "dwd_session_logs")
    @NotBlank(message = "表名不能为空")
    private String tableName;
    
    @Schema(description = "数据保留天数", required = true, example = "90")
    @NotNull(message = "数据保留天数不能为空")
    @Min(value = 1, message = "数据保留天数必须大于0")
    private Integer retentionDays;
    
    @Schema(description = "是否启用自动分区", example = "true")
    private Boolean autoPartitionEnabled;
    
    @Schema(description = "分区列名", example = "session_start_time")
    private String partitionColumn;
    
    @Schema(description = "分区粒度", example = "DAY", allowableValues = {"HOUR", "DAY", "WEEK", "MONTH"})
    private String partitionGranularity;
    
    @Schema(description = "热分区数量", example = "7")
    @Min(value = 0, message = "热分区数量不能为负数")
    private Integer hotPartitionNum;
    
    @Schema(description = "温分区数量", example = "30")
    @Min(value = 0, message = "温分区数量不能为负数")
    private Integer warmPartitionNum;
    
    @Schema(description = "冷分区数量", example = "53")
    @Min(value = 0, message = "冷分区数量不能为负数")
    private Integer coldPartitionNum;
    
    @Schema(description = "是否启用自动清理", example = "true")
    private Boolean autoCleanupEnabled;
    
    @Schema(description = "是否启用压缩", example = "true")
    private Boolean compressionEnabled;
    
    @Schema(description = "压缩延迟小时数", example = "24")
    @Min(value = 0, message = "压缩延迟小时数不能为负数")
    private Integer compressionDelayHours;
    
    @Schema(description = "压缩阈值", example = "0.8")
    private Double compressionThreshold;
}
