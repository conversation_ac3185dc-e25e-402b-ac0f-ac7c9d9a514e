server:
  port: 8083
  servlet:
    context-path: /data-platform

spring:
  application:
    name: data-platform
  profiles:
    active: dev
  
  # 数据库配置
  datasource:
    url: *********************************************
    username: nta_user
    password: nta_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 2
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# MyBatis Flex配置
mybatis-flex:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.geeksec.dataplatform.domain.model
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# 日志配置
logging:
  level:
    com.geeksec.dataplatform: DEBUG
    org.springframework.web: INFO
    com.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# API文档配置
knife4j:
  enable: true
  openapi:
    title: NTA 数据平台管理 API
    description: 网络流量分析平台数据平台管理服务接口文档
    version: 3.0.0
    contact:
      name: NTA Team
      email: <EMAIL>
    license:
      name: Apache 2.0
      url: https://www.apache.org/licenses/LICENSE-2.0.html
