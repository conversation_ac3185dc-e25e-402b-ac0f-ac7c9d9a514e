package com.geeksec.admin.interfaces.rest;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.admin.application.SystemManagementApplicationService;
import com.geeksec.admin.interfaces.dto.CleanCondition;
import com.geeksec.admin.interfaces.dto.ProductInfoVo;
import com.geeksec.admin.interfaces.dto.SystemInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;

/**
 * 系统管理控制器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@Tag(name = "系统控制接口", description = "系统管理相关操作")
@RequestMapping("/api/system")
@RequiredArgsConstructor
@Validated
public class SystemController {

    private final SystemManagementApplicationService systemManagementApplicationService;

    /**
     * 关闭主机操作
     * 
     * @param passwordJson 密码信息
     * @return 执行结果
     */
    @PostMapping("/shutdown")
    @Operation(summary = "关闭主机", description = "执行系统关机操作")
    public JSONObject shutdown(@RequestBody JSONObject passwordJson) {
        // 密码不能为null
        if (!passwordJson.containsKey("password")) {
            throw new RuntimeException("密码不能为空");
        }

        // TODO: 验证管理员密码
        String password = passwordJson.getString("password");
        if (!isAdminPassword(password)) {
            throw new RuntimeException("密码错误");
        }

        return systemManagementApplicationService.shutdown();
    }

    /**
     * 重启主机操作
     * 
     * @param passwordJson 密码信息
     * @return 执行结果
     */
    @PostMapping("/reboot")
    @Operation(summary = "重启主机", description = "重启主机操作")
    public JSONObject reboot(@RequestBody JSONObject passwordJson) {
        // 密码不能为null
        if (!passwordJson.containsKey("password")) {
            throw new RuntimeException("密码不能为空");
        }

        // TODO: 验证管理员密码
        String password = passwordJson.getString("password");
        if (!isAdminPassword(password)) {
            throw new RuntimeException("密码错误");
        }

        return systemManagementApplicationService.reboot();
    }

    /**
     * 修改密码 (暂未实现)
     *
     * @param passwordJson 密码信息
     * @return 执行结果
     */
    @PostMapping("/change/password")
    @Operation(summary = "修改密码", description = "修改密码操作")
    public JSONObject changePassword(@RequestBody JSONObject passwordJson) {
        JSONObject response = new JSONObject();
        response.put("result_code", "1");
        response.put("result_desc", "密码修改功能暂未实现");
        return response;
    }

    /**
     * 获取磁盘信息
     *
     * @return 磁盘信息
     */
    @PostMapping("/disk/info")
    @Operation(summary = "获取磁盘信息", description = "获取磁盘信息操作")
    public JSONObject getDiskInfo() {
        return systemManagementApplicationService.getManageableDisks();
    }

    /**
     * 获取系统信息
     *
     * @return 系统信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取系统信息", description = "获取系统信息操作")
    public JSONObject getSystemInfo() {
        return systemManagementApplicationService.getSystemInfo();
    }

    // 产品信息、数据清理、系统重置功能已移除（属于数据访问服务范畴）

    /**
     * 更新磁盘 (暂未实现)
     *
     * @return 执行结果
     */
    @PostMapping("/disk/change")
    @Operation(summary = "更新磁盘", description = "更新磁盘操作")
    public JSONObject diskChange() {
        JSONObject response = new JSONObject();
        response.put("result_code", "1");
        response.put("result_desc", "磁盘更新功能暂未实现");
        return response;
    }

    /**
     * 重组磁盘
     *
     * @return 执行结果
     */
    @PostMapping("/disk/rebuild")
    @Operation(summary = "重组磁盘", description = "重组磁盘操作")
    public JSONObject diskRebuild() {
        return systemManagementApplicationService.rebuildDisk();
    }

    /**
     * 准备挂载磁盘
     *
     * @return 执行结果
     */
    @PostMapping("/disk/mount/ready")
    @Operation(summary = "准备挂载磁盘", description = "准备挂载磁盘操作")
    public JSONObject diskMountReady() {
        return systemManagementApplicationService.prepareMount();
    }

    /**
     * 挂载磁盘
     *
     * @return 执行结果
     */
    @PostMapping("/disk/mount/data")
    @Operation(summary = "挂载磁盘", description = "挂载磁盘操作")
    public JSONObject diskMountData() {
        return systemManagementApplicationService.mountDataDisk();
    }

    // 动态库检测功能已移至 LibraryController
    // 磁盘状态和字段查询功能暂未实现

    /**
     * 验证管理员密码
     * 
     * @param password 密码
     * @return 是否为管理员密码
     */
    private boolean isAdminPassword(String password) {
        // TODO: 实现密码验证逻辑
        return true;
    }
}
