package com.geeksec.admin.domain.service;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.admin.domain.model.NetworkConfig;

/**
 * 运维管理领域服务接口 - 统一的运维管理领域服务
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface OperationManagementDomainService {
    
    // ==================== 网络管理相关 ====================
    
    /**
     * 修改IP配置
     *
     * @param config 网络配置
     * @return 操作结果
     */
    JSONObject modifyIpConfig(NetworkConfig config);
    
    /**
     * 设置NTP服务器
     *
     * @param ntpServer NTP服务器地址
     * @return 操作结果
     */
    JSONObject setNtpServer(String ntpServer);
    
    /**
     * 获取网络配置信息
     *
     * @return 网络配置
     */
    NetworkConfig getNetworkConfig();
    
    /**
     * 获取网络设备信息
     *
     * @param deviceName 设备名称
     * @return 设备信息
     */
    JSONObject getNetworkDeviceInfo(String deviceName);
    
    /**
     * 获取所有网络接口信息
     *
     * @return 网络接口列表
     */
    JSONObject getAllNetworkInterfaces();
    
    // ==================== 动态库检测相关 ====================
    
    /**
     * 检测动态库文件
     *
     * @param ruleId 规则ID
     * @return 检测结果
     */
    JSONObject checkSoFiles(Integer ruleId);
    
    /**
     * 检测Docker动态库文件
     *
     * @param path 路径
     * @return 检测结果
     */
    JSONObject checkDockerSoFiles(String path);
    
    /**
     * 检测系统动态库依赖
     *
     * @param libraryPath 库文件路径
     * @return 检测结果
     */
    JSONObject checkLibraryDependencies(String libraryPath);
    
    /**
     * 获取系统已安装的动态库列表
     *
     * @return 动态库列表
     */
    JSONObject getInstalledLibraries();
    
    // ==================== 系统操作相关 ====================
    
    /**
     * 关闭主机
     *
     * @return 执行状态
     */
    JSONObject shutdown();
    
    /**
     * 重启主机
     *
     * @return 执行状态
     */
    JSONObject reboot();
    
    /**
     * 修改密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 执行状态
     */
    JSONObject changePassword(String userName, String password);
    
    /**
     * 获取磁盘使用情况
     *
     * @return 磁盘信息
     */
    JSONObject getDiskInfoData();
    
    /**
     * 获取系统负载信息
     *
     * @return 系统负载
     */
    JSONObject getSystemLoad();
    
    /**
     * 获取进程信息
     *
     * @return 进程列表
     */
    JSONObject getProcessInfo();
    
    /**
     * 获取服务状态
     *
     * @param serviceName 服务名称
     * @return 服务状态
     */
    JSONObject getServiceStatus(String serviceName);
    
    /**
     * 启动服务
     *
     * @param serviceName 服务名称
     * @return 操作结果
     */
    JSONObject startService(String serviceName);
    
    /**
     * 停止服务
     *
     * @param serviceName 服务名称
     * @return 操作结果
     */
    JSONObject stopService(String serviceName);
    
    /**
     * 重启服务
     *
     * @param serviceName 服务名称
     * @return 操作结果
     */
    JSONObject restartService(String serviceName);
    
    /**
     * 获取系统日志
     *
     * @param logType 日志类型
     * @param lines 行数
     * @return 日志内容
     */
    JSONObject getSystemLogs(String logType, Integer lines);
    
    /**
     * 清理系统缓存
     *
     * @return 清理结果
     */
    JSONObject clearSystemCache();
    
    /**
     * 获取系统性能指标
     *
     * @return 性能指标
     */
    JSONObject getPerformanceMetrics();
}
