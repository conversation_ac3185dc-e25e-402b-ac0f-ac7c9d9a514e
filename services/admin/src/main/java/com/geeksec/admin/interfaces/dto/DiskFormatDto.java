package com.geeksec.admin.interfaces.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 磁盘格式化DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "磁盘格式化请求")
public class DiskFormatDto {
    
    @Schema(description = "设备路径", required = true, example = "/dev/sdb1")
    @NotBlank(message = "设备路径不能为空")
    private String devicePath;
    
    @Schema(description = "文件系统类型", required = true, example = "ext4", 
            allowableValues = {"ext4", "xfs", "btrfs"})
    @NotBlank(message = "文件系统类型不能为空")
    private String fileSystemType;
    
    @Schema(description = "是否强制格式化", example = "false")
    private Boolean force = false;
    
    @Schema(description = "格式化标签", example = "data_disk")
    private String label;
}
