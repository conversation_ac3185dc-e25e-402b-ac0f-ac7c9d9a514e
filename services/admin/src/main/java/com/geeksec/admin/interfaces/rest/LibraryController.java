package com.geeksec.admin.interfaces.rest;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.admin.infrastructure.service.LibraryCheckService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 动态库检测控制器
 * 提供动态库检测和依赖分析功能
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@Tag(name = "动态库检测接口", description = "动态库检测和依赖分析相关操作")
@RequestMapping("/api/library")
public class LibraryController {

    @Autowired
    private LibraryCheckService libraryCheckService;



    /**
     * 检测动态库文件
     *
     * @param request 请求参数
     * @return 检测结果
     */
    @PostMapping("/check/so")
    @Operation(summary = "检测动态库文件", description = "检测动态库文件操作")
    public ApiResponse<JSONObject> checkSoFiles(@RequestBody(required = false) JSONObject request) {
        try {
            Integer ruleId = null;
            if (request != null) {
                ruleId = request.getInteger("rule_id");
            }
            JSONObject result = libraryCheckService.checkSoFiles(ruleId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("检测动态库文件失败", e);
            return ApiResponse.error("检测动态库文件失败: " + e.getMessage());
        }
    }

    /**
     * 检测Docker动态库文件
     *
     * @param request 请求参数
     * @return 检测结果
     */
    @PostMapping("/check/docker/so")
    @Operation(summary = "检测Docker动态库文件", description = "检测Docker动态库文件操作")
    public ApiResponse<JSONObject> checkDockerSoFiles(@RequestBody JSONObject request) {
        try {
            String path = request.getString("path");

            if (path == null || path.trim().isEmpty()) {
                return ApiResponse.error("路径不能为空");
            }

            JSONObject result = libraryCheckService.checkDockerSoFiles(path);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("检测Docker动态库文件失败", e);
            return ApiResponse.error("检测Docker动态库文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统已安装的动态库列表
     *
     * @return 动态库列表
     */
    @GetMapping("/installed")
    @Operation(summary = "获取系统已安装的动态库列表", description = "获取系统已安装的动态库列表操作")
    public ApiResponse<JSONObject> getInstalledLibraries() {
        try {
            JSONObject result = libraryCheckService.getInstalledLibraries();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取系统已安装的动态库列表失败", e);
            return ApiResponse.error("获取系统已安装的动态库列表失败: " + e.getMessage());
        }
    }

    /**
     * 检测库文件依赖
     *
     * @param request 请求参数
     * @return 检测结果
     */
    @PostMapping("/check/dependencies")
    @Operation(summary = "检测库文件依赖", description = "检测库文件依赖操作")
    public ApiResponse<JSONObject> checkLibraryDependencies(@RequestBody JSONObject request) {
        try {
            String libraryPath = request.getString("library_path");

            if (libraryPath == null || libraryPath.trim().isEmpty()) {
                return ApiResponse.error("库文件路径不能为空");
            }

            JSONObject result = libraryCheckService.checkLibraryDependencies(libraryPath);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("检测库文件依赖失败", e);
            return ApiResponse.error("检测库文件依赖失败: " + e.getMessage());
        }
    }
}
